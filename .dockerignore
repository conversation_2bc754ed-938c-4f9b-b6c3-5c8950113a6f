# Version control
.git
.gitignore

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs and databases
logs/
*.log
debug/
*.db
*.sqlite
*.sqlite3

# Local development
.env*
.python-version

# Debug files
debug/

# Test coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

# ML/LLM specific
.cache/
.pytorch_cache/
.transformers_cache/
*.ckpt
*.pth
*.pt
model_weights/
saved_models/
wandb/

.idea/
.git/
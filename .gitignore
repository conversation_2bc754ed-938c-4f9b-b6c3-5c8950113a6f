# Environment variables
.env
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
.Python
*.so
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# ML/LLM specific
.cache/
.pytorch_cache/
.transformers_cache/
*.ckpt
*.pth
*.pt
model_weights/
saved_models/
wandb/

# Logs and databases
logs/
*.log
debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.db
*.sqlite
*.sqlite3

# Local development
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Debug files
debug/
debug.log

# Test coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Distribution / packaging
*.zip
*.tar.gz
*.rar
*.7z


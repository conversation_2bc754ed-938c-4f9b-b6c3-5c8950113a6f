# ------------------------------------------------
# BASE IMAGE: Hugging Face Transformers + PyTorch + CUDA + Python
# ------------------------------------------------
    FROM huggingface/transformers-pytorch-gpu:latest

    # Optional: Set environment configs
    ENV PYTHONUNBUFFERED=1 \
        PYTHONDONTWRITEBYTECODE=1
    
    # Install system dependencies (if needed)
    RUN apt-get update && apt-get install -y --no-install-recommends \
        libgl1 \
        ffmpeg \
        && apt-get clean && rm -rf /var/lib/apt/lists/*
    
    # Install Python dependencies
    COPY requirements.txt .
    RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
        && pip install --no-cache-dir -r requirements.txt
    
    # Copy your app
    COPY . /app
    WORKDIR /app
    
    # Expose FastAPI port
    EXPOSE 8000
    
    # Run your FastAPI app using uvicorn
    CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
    
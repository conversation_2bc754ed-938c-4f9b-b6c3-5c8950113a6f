# Configuration management
from typing import List, Dict, Optional
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Debug settings
    debug_mode: bool = False
    
    # Application settings
    app_name: str = "ML Processing Service"
    ai_model: Optional[str] = None
    hf_token: Optional[str] = None
    max_batch_size: int = 10
    processing_timeout: int = 300
    
    # CORS settings
    cors_origins: List[str] = ["http://localhost:3000", "https://contjobmlservice.icysand-17d03f8a.westus3.azurecontainerapps.io"]
    
    # Hardware settings
    device: str = "auto"
    max_memory: Dict[int, str] = {}
    
    # Generation settings - updated with env vars and reasonable defaults
    max_length: int = Field(
        default=32768,
        validation_alias='MAX_LENGTH',
        description="Maximum length of input tokens"
    )
    max_new_tokens: int = Field(
        default=32768,
        validation_alias='MAX_NEW_TOKENS',
        description="Maximum number of new tokens to generate"
    )
    temperature: float = Field(
        default=0.0,
        validation_alias='TEMPERATURE',
        ge=0.0,
        le=1.0
    )
    do_sample: bool = Field(
        default=False,
        validation_alias='DO_SAMPLE'
    )
    top_k: Optional[int] = Field(
        default=None,
        validation_alias='TOP_K'
    )
    top_p: Optional[float] = Field(
        default=None,
        validation_alias='TOP_P'
    )
    
    # Cloud environment settings
    is_cloud_environment: bool = False
    cloud_gpu_count: int = 0
    cloud_gpu_memory_gb: float = 0.0
    
    # Fallback settings
    force_cpu: bool = False
    
    # Logging settings
    log_retention_days: int = 7

    # Database settings
    db_user: str = Field(default="", validation_alias="DB_USER")
    db_password: str = Field(default="", validation_alias="DB_PASSWORD")
    db_host: str = Field(default="localhost", validation_alias="DB_HOST")
    db_port: int = Field(default=5433, validation_alias="DB_PORT")
    db_name: str = Field(default="edisonjobs", validation_alias="DB_NAME")
    
    # Encryption
    encryption_password: str = Field(default="", validation_alias="ENCRYPTION_PASSWORD")

    @property
    def database_url(self) -> str:
        return f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    class Config:
        env_file = ".env"

    def validate_required_settings(self):
        """Validate that required settings are set"""
        if not self.ai_model:
            raise ValueError("AI_MODEL must be set in environment variables or .env file")
        if not self.hf_token:
            raise ValueError("HF_TOKEN must be set in environment variables or .env file")

settings = Settings()
settings.validate_required_settings()

import asyncio
import time
import json
import uuid
import torch
import os
import asyncpg
import sys # For sys.exc_info()
from typing import Dict, Any
from datetime import datetime, timezone
from ..processor.processor import LLMProcessor
from ..prompts.resume_evaluator_prompt import RESUME_EVALUATOR_PROMPT
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ...manager.ml_manager import MLManager
from fastapi import BackgroundTasks
from app.config.settings import settings
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class ResumeEvaluator:
    def __init__(self, ml_manager: MLManager):
        self.llm = LLMProcessor()
        self.ml_manager = ml_manager
        self._validate_initialization()

    def _validate_initialization(self) -> None:
        if not self.llm.initialized:
            logger.error("LLM processor not properly initialized")
            raise RuntimeError("LLM processor initialization failed")

    def _log_to_eval_debug_file(self, debug_path: str, request_id: str, level_str: str, message_str: str, exc_info_tuple=None):
        """Helper method to append formatted log messages to a specific debug file for evaluation."""
        try:
            log_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3] + "Z"
            current_request_id = request_id if request_id else 'NO_REQ_ID_EVAL'
            logger_name = logger.name # module logger's name
            
            log_prefix = f"{log_timestamp} [{current_request_id}] [{level_str.upper()}] [{logger_name}] "
            
            full_log_message = log_prefix + message_str
            if exc_info_tuple:
                import traceback # Import locally
                formatted_exception_trace = "".join(traceback.format_exception(exc_info_tuple[0], exc_info_tuple[1], exc_info_tuple[2]))
                full_log_message += "\n" + formatted_exception_trace
        
            with open(debug_path, 'a', encoding='utf-8') as f_debug:
                f_debug.write(full_log_message + "\n")
        except Exception as e_log_write_fail:
            # Log to console if file logging fails
            logger.error(
                f"CRITICAL: Failed to write to eval debug log file {debug_path}. "
                f"Original log: [{current_request_id}] [{level_str.upper()}] {message_str}. Error: {e_log_write_fail}"
            )

    async def evaluate(self, resume_data: Dict[str, Any], userId: str, resumeId: str, background_tasks: BackgroundTasks) -> Dict[str, Any]:
        """
        Evaluate a single resume with proper error handling and cancellation support
        """
        start_time = time.time()
        request_id = str(uuid.uuid4())
        debug_path = f"debug/llm_response_EVAL_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        os.makedirs('debug', exist_ok=True)

        initial_msg = f"Evaluation request initiated for userId: {userId}, resumeId: {resumeId}"
        logger.info(f"[{request_id}] {initial_msg}")
        self._log_to_eval_debug_file(debug_path, request_id, "INFO", initial_msg)
        
        try:
            if not self._validate_resume_data(resume_data):
                error_msg = "Invalid resume data format for evaluation."
                logger.error(f"[{request_id}] {error_msg}")
                self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_msg)
                return self._create_error_response(error_msg, request_id=request_id, start_time=start_time)

            prompt = self._create_evaluation_prompt(resume_data)
            
            try:
                response = await self.llm.process(prompt, request_id)

                logger.info(f"[{request_id}] LLM Response, {response}")
                
                # Log the raw LLM response to the debug file (append mode via helper)
                if response and "response" in response:
                    llm_raw_response_content = response["response"]
                    self._log_to_eval_debug_file(debug_path, request_id, "INFO", f"--- LLM RAW EVALUATION RESPONSE (received at {datetime.now(timezone.utc).isoformat()}) ---\n{llm_raw_response_content}\n--- END LLM RAW EVALUATION RESPONSE ---")
                else:
                    self._log_to_eval_debug_file(debug_path, request_id, "WARN", "LLM evaluation response object was None or did not contain 'response' key.")

                if not response or "response" not in response:
                    error_msg = "Invalid LLM response structure from evaluation model."
                    logger.error(f"[{request_id}] {error_msg}")
                    self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_msg)
                    return self._create_error_response(error_msg, request_id, start_time)
            
                parsed_result =  extract_and_sanitize_json_from_llm_output(response["response"])
                
                # Validate and fix scores
                evaluation = self._validate_and_fix_scores(parsed_result, request_id, debug_path)
                
                # Save to database in background
                background_tasks.add_task(
                    self._save_evaluation_to_database,
                    evaluation_result=evaluation,
                    userId=userId,
                    resumeId=resumeId,
                    request_id=request_id, # Pass request_id
                    debug_path=debug_path  # Pass debug_path
                )
                
                return self._format_evaluation_result(
                    evaluation,
                    request_id=request_id,
                    start_time=start_time
                )

            except asyncio.TimeoutError:
                error_msg = "Evaluation timed out"
                logger.error(f"[{request_id}] {error_msg}")
                self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_msg, exc_info_tuple=sys.exc_info())
                return self._create_error_response(error_msg, request_id=request_id, start_time=start_time)

        except Exception as e:
            error_msg = f"Evaluation error: {str(e)}"
            logger.error(f"[{request_id}] {error_msg}")
            # Log full traceback to console
            logger.exception(f"[{request_id}] Full traceback for evaluation error:")
            # Log error and traceback to debug file
            self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_msg, exc_info_tuple=sys.exc_info())
            return self._create_error_response(str(e), request_id=request_id, start_time=start_time)


    def _validate_resume_data(self, resume_data: Dict[str, Any]) -> bool:
        """Validate the resume data structure"""
        return isinstance(resume_data, dict)

    def _create_evaluation_prompt(self, resume_data: Dict[str, Any]) -> str:
        """Create a structured prompt for resume evaluation"""
        return RESUME_EVALUATOR_PROMPT.format(
            resume_data=json.dumps(resume_data, indent=2)
        )

    def _format_evaluation_result(self, result: Dict[str, Any], request_id: str, start_time: float) -> Dict[str, Any]:
        """Format the evaluation result with metadata"""
        return {
            "request_id": request_id,
            "processing_time": time.time() - start_time,
            "status": "success",
            "evaluation": result
        }

    def _create_error_response(self, error_message: str, request_id: str, start_time: float) -> Dict[str, Any]:
        """Create a standardized error response"""
        return {
            "request_id": request_id,
            "processing_time": time.time() - start_time,
            "status": "error",
            "error": error_message,
            "evaluation": {
                "scores": {
                    "experience_quality": 0,
                    "education": 0,
                    "skills": 0,
                    "completeness": 0,
                    "overall": 0
                },
                "assessment": {
                    "strengths": [],
                    "improvements": ["Unable to evaluate resume"],
                    "industry_recommendations": []
                },
                "evaluation_details": {
                    "experience": "Evaluation failed",
                    "education": "Evaluation failed",
                    "skills": "Evaluation failed"
                }
            }
        }

    def _cleanup_resources(self) -> None:
        """Clean up resources after processing"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    def _calculate_overall_score(self, scores: Dict[str, int]) -> int:
        """Calculate weighted overall score following the scoring rules"""
        weights = {
            'experience_quality': 0.35,
            'education': 0.25,
            'skills': 0.25,
            'completeness': 0.15
        }
        
        weighted_sum = sum(
            scores.get(key, 0) * weight 
            for key, weight in weights.items()
        )
        
        # Round to nearest integer
        overall = round(weighted_sum)
        
        # Never return 0 if any component score is above 0
        if overall == 0 and any(scores.get(key, 0) > 0 for key in weights.keys()):
            max_component = max(scores.get(key, 0) for key in weights.keys())
            min_weight = min(weights.values())
            overall = round(max_component * min_weight)
        
        return overall

    def _validate_and_fix_scores(self, evaluation: Dict[str, Any], request_id: str, debug_path: str) -> Dict[str, Any]:
        """Validate and fix evaluation scores according to scoring rules"""
        if 'scores' in evaluation:
            scores = evaluation['scores']
            
            # Calculate correct overall score
            correct_overall = self._calculate_overall_score(scores)
            original_overall = scores.get('overall')
            
            # Update the overall score if it doesn't match our calculation
            if original_overall != correct_overall:
                warn_msg = f"Fixing incorrect overall score from {original_overall} to {correct_overall}"
                logger.warning(f"[{request_id}] {warn_msg}")
                self._log_to_eval_debug_file(debug_path, request_id, "WARN", warn_msg)
                scores['overall'] = correct_overall
                
            evaluation['scores'] = scores
            
        return evaluation
    
    async def _save_evaluation_to_database(self, evaluation_result: Dict[str, Any], userId: str, resumeId: str, request_id: str, debug_path: str) -> None:
        """Save evaluation results to database"""
        
        log_prefix = f"[{request_id}]" if request_id else "[NO_REQ_ID_EVAL_SAVE]"
        info_msg_db_save = f"Attempting to save evaluation to DB for userId: {userId}, resumeId: {resumeId}"
        logger.info(f"{log_prefix} {info_msg_db_save}")
        self._log_to_eval_debug_file(debug_path, request_id, "INFO", info_msg_db_save)
        
        conn = None
        try:
            conn = await asyncpg.connect(settings.database_url)
            
            # Convert evaluation_result to string
            evaluation_data_str = json.dumps(evaluation_result)
            
            # Extract individual fields from evaluation_result
            overall_score = evaluation_result.get('scores', {}).get('overall')
            completeness_score = evaluation_result.get('scores', {}).get('completeness')
            experience_quality_score = evaluation_result.get('scores', {}).get('experience_quality')
            education_score = evaluation_result.get('scores', {}).get('education')
            skills_score = evaluation_result.get('scores', {}).get('skills')
            
            # Extract assessment fields
            strengths = json.dumps(evaluation_result.get('assessment', {}).get('strengths', []))
            improvements = json.dumps(evaluation_result.get('assessment', {}).get('improvements', []))
            recommendations = json.dumps(evaluation_result.get('assessment', {}).get('industry_recommendations', []))
            
            # Extract evaluation details
            experience_evaluation = evaluation_result.get('evaluation_details', {}).get('experience')
            education_evaluation = evaluation_result.get('evaluation_details', {}).get('education')
            skills_evaluation = evaluation_result.get('evaluation_details', {}).get('skills')

            # Log the data being prepared for DB (optional, can be verbose)
            # data_to_log_for_db = {
            #     "overallScore": overall_score, "completenessScore": completeness_score, 
            #     "experienceQualityScore": experience_quality_score, "educationScore": education_score, 
            #     "skillsScore": skills_score, "strengths": strengths, "improvements": improvements, 
            #     "recommendations": recommendations, "experienceEvaluation": experience_evaluation, 
            #     "educationEvaluation": education_evaluation, "skillsEvaluation": skills_evaluation,
            #     "evaluationDataLength": len(evaluation_data_str) if evaluation_data_str else 0
            # }
            # self._log_to_eval_debug_file(debug_path, request_id, "DEBUG", f"Data prepared for UserResumeEvaluation: {json.dumps(data_to_log_for_db)}")
            
            # Check if record exists
            existing_record = await conn.fetchrow(
                'SELECT id FROM "UserResumeEvaluation" WHERE "resumeId" = $1', # Query by resumeId only
                resumeId
            )
            
            if existing_record:
                # Update existing record
                record_id = existing_record['id']
                update_msg = f"Updating existing evaluation record: {record_id}"
                logger.info(f"{log_prefix} {update_msg}")
                self._log_to_eval_debug_file(debug_path, request_id, "INFO", update_msg)
                
                await conn.execute('''
                    UPDATE "UserResumeEvaluation" SET
                        "overallScore" = $1,
                        "completenessScore" = $2,
                        "experienceQualityScore" = $3,
                        "educationScore" = $4,
                        "skillsScore" = $5,
                        "strengths" = $6,
                        "improvements" = $7,
                        "recommendations" = $8,
                        "experienceEvaluation" = $9,
                        "educationEvaluation" = $10,
                        "skillsEvaluation" = $11,
                        "evaluationData" = $12,
                        "updatedAt" = $13
                    WHERE id = $14
                ''',
                    overall_score,
                    completeness_score,
                    experience_quality_score,
                    education_score,
                    skills_score,
                    strengths,
                    improvements,
                    recommendations,
                    experience_evaluation,
                    education_evaluation,
                    skills_evaluation,
                    evaluation_data_str,
                    datetime.now(),
                    record_id
                )
            else:
                # Insert new record
                logger.info(f"Creating new evaluation record for userId: {userId}, resumeId: {resumeId}")
                create_msg = f"Creating new evaluation record for userId: {userId}, resumeId: {resumeId}"
                logger.info(f"{log_prefix} {create_msg}")
                self._log_to_eval_debug_file(debug_path, request_id, "INFO", create_msg)
                
                await conn.execute('''
                    INSERT INTO "UserResumeEvaluation" (
                        id, "resumeId", "userId", "overallScore", "completenessScore", 
                        "experienceQualityScore", "educationScore", "skillsScore", 
                        "strengths", "improvements", "recommendations", 
                        "experienceEvaluation", "educationEvaluation", "skillsEvaluation", 
                        "evaluationData"
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                ''', 
                    str(uuid.uuid4()), 
                    resumeId,
                    userId,
                    overall_score,
                    completeness_score,
                    experience_quality_score,
                    education_score,
                    skills_score,
                    strengths,
                    improvements,
                    recommendations,
                    experience_evaluation,
                    education_evaluation,
                    skills_evaluation,
                    evaluation_data_str
                )
            
        except asyncpg.PostgresError as db_pg_error:
            error_summary = (
                f"PostgreSQL error during save to UserResumeEvaluation: {str(db_pg_error)}\n"
                f"SQLSTATE: {getattr(db_pg_error, 'sqlstate', 'N/A (attribute missing)')}\n"
                f"Details: {getattr(db_pg_error, 'details', 'N/A (attribute missing)')}\n"
                f"Query: {getattr(db_pg_error, 'query', 'N/A (attribute missing)')}"
            )
            logger.error(f"{log_prefix} {error_summary}")
            logger.exception(f"{log_prefix} Full traceback for UserResumeEvaluation save failure (PostgresError):")
            self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_summary, exc_info_tuple=sys.exc_info())
            
        except Exception as e:
            error_summary = f"General error during save to UserResumeEvaluation: {str(e)}"
            logger.error(f"{log_prefix} {error_summary}")
            logger.exception(f"{log_prefix} Full traceback for UserResumeEvaluation save failure (General Exception):")
            self._log_to_eval_debug_file(debug_path, request_id, "ERROR", error_summary, exc_info_tuple=sys.exc_info())
        finally:
            if conn:
                await conn.close()

# Match Job to Resumes
import uuid
import os
import json
import re
import asyncpg
from typing import Dict, Any, List, Optional
from datetime import datetime
from ..processor.processor import LLMProcessor
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ..prompts.job_to_resumes_prompt import JOB_TO_RESUMES_PROMPT
from app.config.settings import settings

from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class JobToResumesMatcher:
    def __init__(self):
        self.llm = LLMProcessor()
        self.logger = get_logger(__name__)
        # Default weights based on existing implementation
        self.default_weights = {
            'skills_match': 0.40,
            'experience_alignment': 0.35,
            'education_fit': 0.25
        }
    
    def _calculate_overall_score(self, scores: Dict[str, int], weights: Optional[Dict[str, float]] = None) -> int:
        """Calculate weighted overall score following the scoring rules"""
        # Use provided weights or defaults
        weights = weights or self.default_weights
        
        weighted_sum = sum(
            scores.get(key, 0) * weight 
            for key, weight in weights.items()
        )
        
        # Round to nearest integer
        overall = round(weighted_sum)
        
        # Never return 0 if any component score is above 0
        if overall == 0 and any(scores.get(key, 0) > 0 for key in weights.keys()):
            max_component = max(scores.get(key, 0) for key in weights.keys())
            min_weight = min(weights.values())
            overall = round(max_component * min_weight)
        
        return overall

    def _validate_and_fix_match_scores(self, match: Dict[str, Any], weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Validate and fix match scores according to scoring rules"""
        if 'match_scores' in match:
            scores = match['match_scores']
            
            # Calculate correct overall score
            correct_overall = self._calculate_overall_score(scores, weights)
            
            # Update the overall score if it doesn't match our calculation
            if scores.get('overall_score', 0) != correct_overall:
                self.logger.warning(f"Fixing incorrect overall score from {scores.get('overall_score')} to {correct_overall}")
                scores['overall_score'] = correct_overall
                
            match['match_scores'] = scores
            
        return match

    async def match(self, jobId: str, job_data: Dict[str, Any], resumes: List[Dict[str, Any]], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Match a job to multiple resumes"""
        start_time = datetime.now()
        request_id = str(uuid.uuid4())
        # Request ID is now automatically handled by thread-local storage in logging_helpers
        
        # Log the input data to help debug
        self.logger.info(f"Starting job-to-resumes matching with jobId: {jobId}")
        self.logger.info(f"Number of resumes to match: {len(resumes)}")
        for idx, resume in enumerate(resumes):
            self.logger.info(f"Resume {idx+1} ID: {resume.get('id', 'unknown')}")
        
        # Extract custom weights from options if provided
        custom_weights = None
        if options and all(key in options for key in self.default_weights.keys()):
            try:
                custom_weights = {
                    key: float(options[key]) 
                    for key in self.default_weights.keys()
                }
                # Log that we're using custom weights
                self.logger.info(f"Using custom weights: {custom_weights}")
            except (ValueError, TypeError):
                self.logger.warning(f"Invalid custom weights in options, using defaults")
        
        try:
            # Create prompt with custom weights if available
            prompt = self._create_matching_prompt(job_data, resumes, custom_weights)
            result = await self.llm.process(prompt, request_id)
            
            # Save raw response for debugging
            debug_path = f"debug/llm_response_JOB_MATCH_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs('debug', exist_ok=True)
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(result["response"])
            
            self.logger.info(f"Raw response saved to: {debug_path}")
            
            try:
                matches = extract_and_sanitize_json_from_llm_output(result["response"])
            except json.JSONDecodeError as e:
                self.logger.warning(f"Initial JSON parsing failed: {e}. Attempting fallback.")
                try:
                    # Attempt to fix common JSON errors like trailing commas
                    # First, extract content within ```json ... ```
                    json_str_match = re.search(r"```json\s*([\s\S]*?)\s*```", result["response"])
                    if json_str_match:
                        json_str = json_str_match.group(1)
                    else:
                        # If no markdown block, assume the whole response is the JSON content
                        json_str = result["response"]
                    
                    # Remove trailing commas
                    json_str_fixed = re.sub(r',\s*([}\]])', r'\1', json_str)
                    matches = json.loads(json_str_fixed)
                    self.logger.info(f"Successfully parsed JSON using fallback method.")
                except Exception as fallback_e:
                    self.logger.error(f"Fallback JSON parsing also failed: {fallback_e}. Re-raising original error.")
                    raise e from fallback_e

            # Validate and fix scores for each match
            if 'matches' in matches:
                # Log the matches returned by the LLM
                for idx, match in enumerate(matches.get('matches', [])):
                    self.logger.info(f"Match {idx+1} resume_id: {match.get('resume_id', 'unknown')}")
                
                # Create a mapping of placeholder IDs ("resume1") to actual resume IDs
                # and a set of valid actual resume IDs from the input
                resume_id_map = {
                    f"resume{idx+1}": resume.get('id')
                    for idx, resume in enumerate(resumes)
                    if resume.get('id') and resume.get('id') != 'unknown' # Ensure valid IDs in map values
                }
                valid_input_actual_ids = {
                    r.get('id') for r in resumes if r.get('id') and r.get('id') != 'unknown'
                }
                
                self.logger.info(f"Resume ID mapping: {resume_id_map}")
                self.logger.info(f"Valid input actual resume IDs: {valid_input_actual_ids}")
                
                # Process each match
                processed_matches = []
                for match_item in matches.get('matches', []):
                    llm_provided_resume_id = match_item.get('resume_id')
                    final_resume_id = None

                    if not llm_provided_resume_id:
                        self.logger.warning(f"LLM match item has no resume_id field. Skipping: {match_item}")
                        continue

                    # Scenario 1: LLM returned a placeholder like "resume1"
                    if llm_provided_resume_id in resume_id_map:
                        final_resume_id = resume_id_map[llm_provided_resume_id]
                        self.logger.info(f"Mapped LLM placeholder '{llm_provided_resume_id}' to actual resume_id '{final_resume_id}'")
                    # Scenario 2: LLM returned an actual resume ID string (UUID)
                    elif llm_provided_resume_id in valid_input_actual_ids:
                        final_resume_id = llm_provided_resume_id
                        self.logger.info(f"LLM returned actual resume_id '{llm_provided_resume_id}', which is a valid input resume ID.")
                    else:
                        self.logger.warning(f"LLM resume_id '{llm_provided_resume_id}' is neither a known placeholder nor a valid input resume ID. Discarding match for: {llm_provided_resume_id}")
                        continue # Skip this match item
                    
                    # Ensure the resolved final_resume_id is truly valid before proceeding
                    if not final_resume_id or final_resume_id == 'unknown': # final_resume_id could be 'unknown' if it came from resume_id_map
                        self.logger.warning(f"Resolved resume_id '{final_resume_id}' is invalid. Discarding match for LLM id: {llm_provided_resume_id}")
                        continue

                    current_match_copy = dict(match_item) # Work on a copy
                    current_match_copy['resume_id'] = final_resume_id
                                        
                    # Validate and fix scores
                    validated_match_item = self._validate_and_fix_match_scores(current_match_copy, custom_weights)
                    processed_matches.append(validated_match_item)
                
                # Replace the matches with the processed ones
                matches['matches'] = processed_matches
                
                # Save each match to the database
                for processed_match_to_save in matches.get('matches', []): # Iterate over the correctly processed matches
                    resume_id = processed_match_to_save.get('resume_id') # This will be the final_resume_id
                    if resume_id:
                        await self._save_match_to_database(
                            job_id=jobId,
                            resume_id=resume_id,
                            match_data=processed_match_to_save,
                            options=options
                        )
                    else: # This else block is now inside the for loop
                        self.logger.error(f"Cannot save match to database: missing resume_id in processed match: {processed_match_to_save}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "request_id": request_id,
                "processing_time": processing_time,
                "status": "success",
                "matches": matches.get("matches", [])
            }
            
        except Exception as e:
            self.logger.error(f"Matching failed: {str(e)}")
            self.logger.exception(e)  # Log the full exception with traceback
            processing_time = (datetime.now() - start_time).total_seconds()
            return {
                "request_id": request_id,
                "processing_time": processing_time,
                "status": "error",
                "error": str(e),
                "matches": []
            }
    
    def _get_scoring_rules_text(self, current_weights: Optional[Dict[str, float]] = None) -> str:
        """Generates the scoring rules text block for the prompt."""
        resolved_weights = current_weights or self.default_weights
        return f"""
**Scoring Rules:**

1. All scores must be between 0 and 100 (inclusive)
2. IMPORTANT! Overall score calculation must be between 0 and 100 (inclusive):
   - Calculate weighted average of all component scores:
     * Skills Match: {resolved_weights.get('skills_match', self.default_weights['skills_match']) * 100:.0f}% weight
     * Experience Alignment: {resolved_weights.get('experience_alignment', self.default_weights['experience_alignment']) * 100:.0f}% weight
     * Education Fit: {resolved_weights.get('education_fit', self.default_weights['education_fit']) * 100:.0f}% weight
   - Round the final score to the nearest integer
   - NEVER return 0 for overall_score if any component score is above 0
""".strip()

    def _create_matching_prompt(self, job: Dict[str, Any], resumes: List[Dict[str, Any]], custom_weights: Optional[Dict[str, float]] = None) -> str:
        """
        Creates the matching prompt.
        Assumes JOB_TO_RESUMES_PROMPT has a {scoring_rules_section} placeholder.
        """
        scoring_rules_content = self._get_scoring_rules_text(custom_weights)
        
        return JOB_TO_RESUMES_PROMPT.format(
            scoring_rules_section=scoring_rules_content,
            job_text=job.get('raw_text', ''),
            resumes_text=self._format_resumes(resumes)
        )
    
    def _format_resumes(self, resumes: List[Dict[str, Any]]) -> str:
        return "\n\n".join([
            f"Resume {idx + 1} (ID: {resume.get('id', 'unknown')}):\n{resume.get('raw_text', '')}"
            for idx, resume in enumerate(resumes)
        ])
    
    async def _save_match_to_database(self, job_id: str, resume_id: str, match_data: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> None:
        """Save match result to database"""
        try:
            self.logger.info(f"Saving match result to database for job: {job_id}, resume: {resume_id}")
            
            # Extract match scores
            match_scores = match_data.get('match_scores', {})
            overall_score = match_scores.get('overall_score')
            skills_match = match_scores.get('skills_match')
            experience_alignment = match_scores.get('experience_alignment')
            education_fit = match_scores.get('education_fit')
            
            # Extract other match data
            match_analysis = match_data.get('match_analysis')
            candidate_strengths = match_data.get('candidate_strengths')
            matching_skills = match_data.get('matching_skills', [])
            missing_requirements = match_data.get('missing_requirements', [])
            experience_relevance = match_data.get('experience_relevance')
            
            # Convert lists to strings
            candidate_strengths_str = json.dumps(candidate_strengths) if isinstance(candidate_strengths, list) else candidate_strengths
            matching_skills_str = json.dumps(matching_skills) if matching_skills else None
            missing_requirements_str = json.dumps(missing_requirements) if missing_requirements else None
            
            # Convert the entire match data to JSON string for storage
            match_result_str = json.dumps(match_data)
            
            # Convert options to JSON string for criteria field
            criteria_str = json.dumps(options) if options else None
            
            # Get current timestamp for both createdAt and updatedAt
            current_time = datetime.now()
            
            # Connect to database
            conn = await asyncpg.connect(settings.database_url)
            
            # Check if record already exists
            existing_record = await conn.fetchrow(
                'SELECT id FROM "JobResumeMatch" WHERE "jobId" = $1 AND "resumeId" = $2',
                job_id, resume_id
            )
            
            if existing_record:
                # Update existing record
                record_id = existing_record['id']
                self.logger.info(f"Updating existing match record for job: {job_id}, resume: {resume_id}, id: {record_id}")
                
                await conn.execute('''
                    UPDATE "JobResumeMatch" SET
                        "criteria" = $1,
                        "overall_score" = $2,
                        "skills_match" = $3,
                        "experience_alignment" = $4,
                        "education_fit" = $5,
                        "match_analysis" = $6,
                        "candidate_strengths" = $7,
                        "matching_skills" = $8,
                        "missing_requirements" = $9,
                        "experience_relevance" = $10,
                        "matchResult" = $11,
                        "updatedAt" = $12
                    WHERE id = $13
                ''',
                    criteria_str,
                    overall_score,
                    skills_match,
                    experience_alignment,
                    education_fit,
                    match_analysis,
                    candidate_strengths_str,
                    matching_skills_str,
                    missing_requirements_str,
                    experience_relevance,
                    match_result_str,
                    current_time,  # updatedAt
                    record_id
                )
            else:
                # Insert new record
                self.logger.info(f"Inserting new match record for job: {job_id}, resume: {resume_id}")
                
                await conn.execute('''
                    INSERT INTO "JobResumeMatch" (
                        id, "jobId", "resumeId", "criteria", "overall_score", "skills_match", 
                        "experience_alignment", "education_fit", "match_analysis", 
                        "candidate_strengths", "matching_skills", "missing_requirements", 
                        "experience_relevance", "matchResult", "createdAt", "updatedAt"
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                ''',
                    str(uuid.uuid4()),
                    job_id,
                    resume_id,
                    criteria_str,
                    overall_score,
                    skills_match,
                    experience_alignment,
                    education_fit,
                    match_analysis,
                    candidate_strengths_str,
                    matching_skills_str,
                    missing_requirements_str,
                    experience_relevance,
                    match_result_str,
                    current_time,  # createdAt
                    current_time   # updatedAt
                )
            
            await conn.close()
            self.logger.info(f"Match result saved to database for job: {job_id}, resume: {resume_id}")
            
        except Exception as db_error:
            self.logger.error(f"Failed to save match result to database for job_id: {job_id}, resume_id: {resume_id}. Error: {str(db_error)}")
            self.logger.exception(db_error) # Log the full traceback for the database error

# Match Resume to Jobs
import os
import uuid
from typing import Dict, Any, List
from datetime import datetime
from ..processor.processor import LLMProcessor
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ..prompts.resume_to_jobs_prompt import RESUME_TO_JOBS_PROMPT
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class ResumeToJobsMatcher:
    def __init__(self):
        self.llm = LLMProcessor()
    
    def _calculate_overall_score(self, scores: Dict[str, int]) -> int:
        """Calculate weighted overall score following the scoring rules"""
        weights = {
            'skills_match': 0.40,
            'experience_alignment': 0.35,
            'education_fit': 0.25
        }
        
        weighted_sum = sum(
            scores.get(key, 0) * weight 
            for key, weight in weights.items()
        )
        
        # Round to nearest integer
        overall = round(weighted_sum)
        
        # Never return 0 if any component score is above 0
        if overall == 0 and any(scores.get(key, 0) > 0 for key in weights.keys()):
            # Return minimum weighted score based on highest component
            max_component = max(scores.get(key, 0) for key in weights.keys())
            min_weight = min(weights.values())
            overall = round(max_component * min_weight)
        
        return overall

    def _validate_and_fix_match_scores(self, match: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and fix match scores according to scoring rules"""
        if 'match_scores' in match:
            scores = match['match_scores']
            
            # Calculate correct overall score
            correct_overall = self._calculate_overall_score(scores)
            
            # Update the overall score if it doesn't match our calculation
            if scores.get('overall_score', 0) != correct_overall:
                logger.warning(f"Fixing incorrect overall score from {scores.get('overall_score')} to {correct_overall}")
                scores['overall_score'] = correct_overall
                
            match['match_scores'] = scores
            
        return match

    async def match(self, resume_data: Dict[str, Any], jobs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Match a resume to multiple jobs"""
        start_time = datetime.now()
        request_id = str(uuid.uuid4())
        
        try:
            prompt = self._create_matching_prompt(resume_data, jobs)
            result = await self.llm.process(prompt, request_id)
            
            # Save raw response for debugging
            debug_path = f"debug/llm_response_RESUME_MATCH_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs('debug', exist_ok=True)
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(result["response"])
            
            logger.info(f"[{request_id}] Raw response saved to: {debug_path}")
            
            matches = extract_and_sanitize_json_from_llm_output(result["response"])
            
            # Validate and fix scores for each match
            if 'matches' in matches:
                matches['matches'] = [
                    self._validate_and_fix_match_scores(match)
                    for match in matches['matches']
                ]
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "request_id": request_id,
                "processing_time": processing_time,
                "status": "success",
                "matches": matches.get("matches", [])
            }
            
        except Exception as e:
            logger.error(f"[{request_id}] Matching failed: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds()
            return {
                "request_id": request_id,
                "processing_time": processing_time,
                "status": "error",
                "error": str(e),
                "matches": []
            }
    
    def _create_matching_prompt(self, resume: Dict[str, Any], jobs: List[Dict[str, Any]]) -> str:
        return RESUME_TO_JOBS_PROMPT.format(
            resume_text=resume.get('raw_text', ''),
            jobs_text=self._format_jobs(jobs)
        )
    
    def _format_jobs(self, jobs: List[Dict[str, Any]]) -> str:
        return "\n\n".join([
            f"Job {idx + 1} (ID: {job.get('id', 'unknown')}):\n{job.get('raw_text', '')}"
            for idx, job in enumerate(jobs)
        ])



import uuid
import os
import asyncpg
import json
from typing import Dict, Any
from datetime import datetime
from ..processor.processor import LLMProcessor
from ...manager.ml_manager import M<PERSON>anager
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ..prompts.job_parser_prompt import JOB_PARSER_PROMPT
from app.config.settings import settings
from fastapi import BackgroundTasks
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class JobParser:
    def __init__(self, ml_manager: MLManager):
        self.llm = LLMProcessor()
        self.ml_manager = ml_manager
        self._validate_initialization()

    def _validate_initialization(self) -> None:
        if not self.llm.initialized:
            logger.error("LLM processor not properly initialized")
            raise RuntimeError("LLM processor initialization failed")

    def _validate_text(self, text: str) -> bool:
        """Validate input text"""
        if not text or not isinstance(text, str):
            return False
        if len(text.strip()) < 10:  # Arbitrary minimum length
            return False
        return True

    async def parse(self, text: str, userId: str, companyId: str, background_tasks: BackgroundTasks) -> Dict[str, Any]:
        if not self._validate_text(text):
            logger.error("Invalid input text")
            return self._get_empty_structure()
        
        request_id = str(uuid.uuid4())
        logger.info(f"[{request_id}] Starting job parsing")
        
        try:
            prompt = JOB_PARSER_PROMPT.format(job_text=text)
            
            logger.debug(f"[{request_id}] Prompt created successfully")
            
            # Send to LLM for processing
            response = await self.llm.process(prompt, request_id)
            
            logger.info(f"[{request_id}] LLM Response, {response}")
            
            # Save raw response for debugging
            debug_path = f"debug/llm_response_JOB_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs('debug', exist_ok=True)
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(response["response"])
            
            if not response or "response" not in response:
                logger.error(f"[{request_id}] Invalid LLM response structure")
                return self._get_empty_structure()
                
            # Extract data from response
            parsed_data = extract_and_sanitize_json_from_llm_output(response["response"])
            
            logger.info(f"[{request_id}] parsed_data, {parsed_data}")
            
            if not parsed_data:
                logger.warning(f"[{request_id}] Failed to parse and sanitize LLM response")
                return self._get_empty_structure()
            
            logger.info(f"PARSED DATA: {userId, parsed_data}")
            
            # Save to database in background if parsed data is not empty
            background_tasks.add_task(
                self._save_to_database,
                parsed_data=parsed_data,
                text=text,
                userId=userId,
                companyId=companyId
            )
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"[{request_id}] Processing error: {str(e)}")
            return self._get_empty_structure()

    def _get_empty_structure(self) -> Dict[str, Any]:
        return {
            "standard_fields": {
                "title": "",
                "company": "",
                "location": "",
                "employment_type": "",
                "experience_level": "",
                "education_required": "",
                "salary_range": {
                    "min": None,
                    "max": None,
                    "currency": None
                },
                "requirements": [],
                "responsibilities": [],
                "skills_required": []
            },
            "additional_sections": {}
        }

    async def _save_to_database(self, parsed_data: Dict[str, Any], text: str, userId: str, companyId: str) -> None:
        """Background task to save resume data to database"""
        
        logger.info(f"Saving resume data to database for user: {userId}")
        logger.info(f"Saving resume data to database: {parsed_data}")
        logger.info(f"Saving resume data to database: {text}")
        
        try:
            conn = await asyncpg.connect(settings.database_url)
            
            # Convert parsed_data to string explicitly
            resume_data_str = json.dumps(parsed_data)
            
            await conn.execute('''
                INSERT INTO "UserJobTemp" (id, "userId", "companyId", "rawText", "jobData")
                VALUES ($1, $2, $3, $4, $5)
            ''', 
            str(uuid.uuid4()), 
            userId,
            companyId,
            text,
            resume_data_str
            )
            
            await conn.close()
            logger.info(f"Resume data saved to database for user: {userId}")
        except Exception as db_error:
            logger.error(f"Failed to save resume to database: {str(db_error)}")
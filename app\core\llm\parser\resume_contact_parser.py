import os
import uuid
import asyncpg
import re
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
import copy # For deepcopy
import asyncio # Add this import
import json
import sys # For sys.exc_info()
from app.config.settings import settings
from app.core.llm.prompts.resume_contact_prompt import RESUME_CONTACT_PROMPT
from app.schemas.prisma_schemas import RESUME_SCHEMA
from app.utils.text_cleaner import lightly_clean_text
from ..processor.processor import LLMProcessor
from ...manager.ml_manager import MLManager
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class ResumeContactInfoParser:
    def __init__(self, ml_manager: MLManager):
        self.llm = LLMProcessor()
        self.ml_manager = ml_manager
        self.logger = get_logger('resume_contact_parser')
        # self._active_processing_file_ids = set() # Instance level, move to class level
        self._validate_initialization()  # Add this call

    def _validate_initialization(self) -> None:
        if not self.llm.initialized:
            logger.error("LLM processor not properly initialized")
            raise RuntimeError("LLM processor initialization failed")

    def _validate_text(self, text: str) -> bool:
        """Validate input text"""
        if not text or not isinstance(text, str):
            return False
        if len(text.strip()) < 10:  # Arbitrary minimum length
            return False
        return True
    
    async def parse(self, text: str) -> Dict[str, Any]:
        request_id = str(uuid.uuid4())
        # Request ID is now automatically handled by thread-local storage in logging_helpers
        
        debug_path = f"debug/llm_response_CONTACT_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        os.makedirs('debug', exist_ok=True) # Ensure directory exists

        initial_info_msg = f"Parse contact info request initiated"
        self.logger.info(initial_info_msg)
        self._log_to_debug_file(debug_path, "INFO", initial_info_msg)
        
        processing_start_msg = "Starting resume contact info parsing logic."
        self.logger.info(processing_start_msg)
        self._log_to_debug_file(debug_path, "INFO", processing_start_msg)
        
        if not self._validate_text(text):  # Add this validation
            msg = "Invalid input text provided for parsing."
            self.logger.error(msg)
            self._log_to_debug_file(debug_path, "ERROR", msg)
            return self._get_empty_structure()
        
        try:
            token_count = len(self.ml_manager.model_singleton.tokenizer.encode(text))
            token_msg = f"Input token count: {token_count}"
            self.logger.info(token_msg)
            self._log_to_debug_file(debug_path, "INFO", token_msg)
            
            # ✅ STEP: Format text for prompt
            text = lightly_clean_text(text)
    
            prompt = RESUME_CONTACT_PROMPT.format(
                resume_text=text
            )
            
            prompt_msg = "Prompt created successfully for LLM."
            self.logger.debug(prompt_msg)
            self._log_to_debug_file(debug_path, "DEBUG", prompt_msg)
            
            response = await self.llm.process(prompt, request_id)
            
            llm_response_received_msg = "LLM Response received."
            self.logger.info(llm_response_received_msg)
            self._log_to_debug_file(debug_path, "INFO", llm_response_received_msg)
            
            # Log the raw LLM response to the debug file
            if response and "response" in response:
                llm_raw_response_content = response["response"]
                self._log_to_debug_file(debug_path, "INFO", f"--- LLM RAW RESPONSE (received at {datetime.now(timezone.utc).isoformat()}) ---\n{llm_raw_response_content}\n--- END LLM RAW RESPONSE ---")
            else:
                self._log_to_debug_file(debug_path, "WARN", "LLM response object was None or did not contain 'response' key.")
            
            if not response or "response" not in response:
                invalid_resp_msg = "Invalid LLM response structure (None or missing 'response' key)."
                self.logger.error(invalid_resp_msg)
                self._log_to_debug_file(debug_path, "ERROR", invalid_resp_msg)
                return self._get_empty_structure()
            
            parsed_data = extract_and_sanitize_json_from_llm_output(response["response"])
            
            self.logger.info(parsed_data)
            parsed_success_msg = "Parsed and sanitized resume data from LLM response successfully."
            self.logger.info(parsed_success_msg)
            self._log_to_debug_file(debug_path, "INFO", parsed_success_msg)
            
            if not parsed_data:
                failed_parse_msg = "Failed to parse and sanitize LLM response (parsed_data is empty)."
                self.logger.warning(failed_parse_msg)
                self._log_to_debug_file(debug_path, "WARN", failed_parse_msg)
                return self._get_empty_structure()
            
            return parsed_data
        
        except Exception as e:
            error_msg = f"Processing error in parse method: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_debug_file(debug_path, "ERROR", error_msg, exc_info_tuple=sys.exc_info()) # Pass exc_info for traceback
            
            self.logger.exception("Full traceback (logged to console):") # Console gets full traceback via logger.exception
            # No need to log traceback twice to file, _log_to_debug_file above handles it if exc_info_tuple is passed
            return self._get_empty_structure()
    
    def _log_to_debug_file(self, debug_path: str, level_str: str, message_str: str, exc_info_tuple=None):
        """Helper method to append formatted log messages to a specific debug file."""
        try:
            log_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3] + "Z"
            
            # Request ID is now handled by thread-local storage
            current_request_id = 'DEBUG_LOG'  # Simple identifier for debug logs
            logger_name = 'parse_contact'
            
            log_prefix = f"{log_timestamp} [{current_request_id}] [{level_str.upper()}] [{logger_name}] "
            
            full_log_message = log_prefix + message_str
            if exc_info_tuple:
                import traceback # Import locally as it's only used here
                formatted_exception_trace = "".join(traceback.format_exception(exc_info_tuple[0], exc_info_tuple[1], exc_info_tuple[2]))
                full_log_message += "\n" + formatted_exception_trace
        
            with open(debug_path, 'a', encoding='utf-8') as f_debug:
                f_debug.write(full_log_message + "\n")
        except Exception as e_log_write_fail:
            # Log to console if file logging fails
            self.logger.logger.error( # Use underlying logger to avoid recursion if adapter is problematic
                f"CRITICAL: Failed to write to debug log file {debug_path}. "
                f"Original log: [{level_str.upper()}] {message_str}. Error: {e_log_write_fail}"
            )
        
    def _get_empty_structure(self) -> Dict[str, Any]:
        """Return empty resume structure with all required fields"""
        return {
            'name': '',
            'email': '',
            'phone': '',
            'title': '',
            'address': '',
            'website': '',
            'linkedin': '',
            'github': '',
            'xaccount': '',
            'summary': ''
        }

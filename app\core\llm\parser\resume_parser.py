import os
import uuid
import asyncpg
import re
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
import copy # For deepcopy
import asyncio # Add this import
import json
import sys # For sys.exc_info()
from app.config.settings import settings
from app.schemas.prisma_schemas import RESUME_SCHEMA
from app.utils.text_cleaner import lightly_clean_text
from ..processor.processor import LLMProcessor
from ...manager.ml_manager import MLManager
from ....utils.encryption_helper import encrypt_to_buffer # Assuming this is the correct path
from ....utils.string_helpers import safe_field, normalize_headers, generate_username
from ....utils.json_helpers import extract_and_sanitize_json_from_llm_output
from ..prompts.resume_parser_prompt_schema import RESUME_PARSER_PROMPT
from fastapi import BackgroundTasks
from ..evaluator.evaluate_resume import ResumeEvaluator
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

def _snake_to_camel_case_dict_keys(data: Any) -> Any:
    """Recursively converts dictionary keys from snake_case to camelCase."""
    if isinstance(data, dict):
        new_dict = {}
        for key, value in data.items():
            new_key = key
            if isinstance(key, str) and '_' in key:
                parts = key.split('_')
                new_key = parts[0] + ''.join(p.capitalize() for p in parts[1:])
            new_dict[new_key] = _snake_to_camel_case_dict_keys(value)
        return new_dict
    elif isinstance(data, list):
        return [_snake_to_camel_case_dict_keys(item) for item in data]
    return data

class ResumeParser:
    _active_processing_file_ids = set()
    _processing_lock = asyncio.Lock()

    def __init__(self, ml_manager: MLManager):
        self.llm = LLMProcessor()
        self.ml_manager = ml_manager
        self.logger = get_logger('resume_parser')
        # self._active_processing_file_ids = set() # Instance level, move to class level
        self._validate_initialization()  # Add this call

    def _validate_initialization(self) -> None:
        if not self.llm.initialized:
            logger.error("LLM processor not properly initialized")
            raise RuntimeError("LLM processor initialization failed")

    def _validate_text(self, text: str) -> bool:
        """Validate input text"""
        if not text or not isinstance(text, str):
            return False
        if len(text.strip()) < 10:  # Arbitrary minimum length
            return False
        return True

    def _standardize_date_format(self, date_str: str) -> str:
        """Standardize date formats to YYYY-MM or YYYY"""
        if not date_str or date_str.lower() == 'present':
            return date_str
        
        # Remove any trailing spaces or periods
        date_str = date_str.strip().rstrip('.')
        
        # Handle ranges like "2012-Present" or "2012-03-2015"
        if '-' in date_str:
            # Split on first hyphen only
            parts = date_str.split('-', 1)
            if len(parts) >= 1:
                return self._standardize_date_format(parts[0])
        
        # Extract year
        year_match = re.search(r'\d{4}', date_str)
        if year_match:
            return year_match.group(0)
        
        return date_str

    def _standardize_experience_dates(self, experience: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Standardize dates in experience entries"""
        for entry in experience:
            if 'start_date' in entry:
                entry['start_date'] = self._standardize_date_format(entry['start_date'])
            if 'end_date' in entry:
                entry['end_date'] = self._standardize_date_format(entry['end_date'])
        return experience

    def _sort_entries_by_date(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Sort entries by date, with most recent first"""
        def get_sort_key(entry):
            start = entry.get('start_date', '')
            # Present should be considered as the most recent
            if not start:
                return ''
            if '-' in start:
                start = start.split('-')[0]
            return start
        
        return sorted(entries, key=get_sort_key, reverse=True)

    async def parse(self, text: str, userId: str, background_tasks: BackgroundTasks, companyId: Optional[str] = None, fileId: Optional[str] = None) -> Dict[str, Any]:
        request_id = str(uuid.uuid4())
        # Request ID is now automatically handled by thread-local storage in logging_helpers
        
        debug_path = f"debug/llm_response_RESUME_{request_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        os.makedirs('debug', exist_ok=True) # Ensure directory exists

        initial_info_msg = f"Parse request initiated for userId: {userId}, fileId: {fileId}, companyId: {companyId}"
        self.logger.info(initial_info_msg)
        self._log_to_debug_file(debug_path, "INFO", initial_info_msg)

        if fileId:
            # 1. Check if already processed and saved in DB
            db_conn_parse = None
            try:
                db_conn_parse = await asyncpg.connect(settings.database_url)
                exists_in_db = await db_conn_parse.fetchval('''
                    SELECT EXISTS (SELECT 1 FROM "UserResume" WHERE "fileId" = $1)
                ''', fileId)
                if exists_in_db:
                    msg = f"File ID {fileId} for user {userId} has already been processed and saved. Ignoring new request."
                    self.logger.info(msg)
                    self._log_to_debug_file(debug_path, "INFO", msg)
                    return self._get_empty_structure()
            except Exception as db_check_exc:
                msg = f"Database check for existing fileId {fileId} failed: {db_check_exc}. Proceeding with parsing."
                self.logger.error(msg)
                self._log_to_debug_file(debug_path, "ERROR", msg, exc_info_tuple=sys.exc_info())
                # Decide if you want to proceed or fail here. For now, proceeding.
            finally:
                if db_conn_parse:
                    await db_conn_parse.close()

            # 2. Check if currently being processed by another concurrent request
            async with ResumeParser._processing_lock:
                if fileId in ResumeParser._active_processing_file_ids:
                    msg = f"File ID {fileId} for user {userId} is currently being processed by another request. Ignoring new request."
                    self.logger.info(msg)
                    self._log_to_debug_file(debug_path, "INFO", msg)
                    return self._get_empty_structure()
                ResumeParser._active_processing_file_ids.add(fileId)
                add_msg = f"Added fileId {fileId} to _active_processing_file_ids."
                self.logger.debug(add_msg) # Usually debug for this level of detail
                self._log_to_debug_file(debug_path, "DEBUG", add_msg)
        
        if not self._validate_text(text):  # Add this validation
            msg = "Invalid input text provided for parsing."
            self.logger.error(msg)
            self._log_to_debug_file(debug_path, "ERROR", msg)
            if fileId: # Ensure cleanup if added to set before validation failure
                async with ResumeParser._processing_lock:
                    ResumeParser._active_processing_file_ids.discard(fileId)
                    discard_msg = f"Invalid text: Discarded fileId {fileId} from _active_processing_file_ids."
                    self.logger.debug(discard_msg)
                    self._log_to_debug_file(debug_path, "DEBUG", discard_msg)
            return self._get_empty_structure()
        
        processing_start_msg = "Starting resume parsing logic."
        self.logger.info(processing_start_msg)
        self._log_to_debug_file(debug_path, "INFO", processing_start_msg)
        try:
            token_count = len(self.ml_manager.model_singleton.tokenizer.encode(text))
            token_msg = f"Input token count: {token_count}"
            self.logger.info(token_msg)
            self._log_to_debug_file(debug_path, "INFO", token_msg)
            
            # ✅ STEP: Format text for prompt
            text = lightly_clean_text(text)
    
            prompt = RESUME_PARSER_PROMPT.format(
                schema=RESUME_SCHEMA,
                resume_text=text
            )
            
            prompt_msg = "Prompt created successfully for LLM."
            self.logger.debug(prompt_msg)
            self._log_to_debug_file(debug_path, "DEBUG", prompt_msg)
            
            response = await self.llm.process(prompt, request_id)
            
            llm_response_received_msg = "LLM Response received."
            self.logger.info(llm_response_received_msg)
            self._log_to_debug_file(debug_path, "INFO", llm_response_received_msg)
            
            # Log the raw LLM response to the debug file
            if response and "response" in response:
                llm_raw_response_content = response["response"]
                self._log_to_debug_file(debug_path, "INFO", f"--- LLM RAW RESPONSE (received at {datetime.now(timezone.utc).isoformat()}) ---\n{llm_raw_response_content}\n--- END LLM RAW RESPONSE ---")
            else:
                self._log_to_debug_file(debug_path, "WARN", "LLM response object was None or did not contain 'response' key.")
            
            if not response or "response" not in response:
                invalid_resp_msg = "Invalid LLM response structure (None or missing 'response' key)."
                self.logger.error(invalid_resp_msg)
                self._log_to_debug_file(debug_path, "ERROR", invalid_resp_msg)
                return self._get_empty_structure()
            
            parsed_data = extract_and_sanitize_json_from_llm_output(response["response"])
            if parsed_data and 'Resume' in parsed_data and 'standard_fields' in parsed_data['Resume']:
                if 'experience' in parsed_data['Resume']['standard_fields']:
                    parsed_data['Resume']['standard_fields']['experience'] = \
                        self._sort_entries_by_date(
                            self._standardize_experience_dates(
                                parsed_data['Resume']['standard_fields']['experience']
                            )
                        )
                if 'education' in parsed_data['Resume']['standard_fields']:
                    parsed_data['Resume']['standard_fields']['education'] = \
                        self._sort_entries_by_date(
                            self._standardize_experience_dates(
                                parsed_data['Resume']['standard_fields']['education']
                            )
                        )
            
            parsed_success_msg = "Parsed and sanitized resume data from LLM response successfully."
            self.logger.info(parsed_success_msg)
            self._log_to_debug_file(debug_path, "INFO", parsed_success_msg)
            
            if not parsed_data:
                failed_parse_msg = "Failed to parse and sanitize LLM response (parsed_data is empty)."
                self.logger.warning(failed_parse_msg)
                self._log_to_debug_file(debug_path, "WARN", failed_parse_msg)
                return self._get_empty_structure()
            
            # Save to database in background if parsed data is not empty
            background_tasks.add_task(
                self._save_to_database_and_evaluate,
                parsed_data=parsed_data,
                text=text,
                userId=userId,
                companyId=companyId,
                fileId=fileId,
                background_tasks=background_tasks,
                debug_path=debug_path # Pass debug_path
            )
            scheduled_db_save_msg = "Scheduled background task for saving to database and evaluation."
            self.logger.info(scheduled_db_save_msg)
            self._log_to_debug_file(debug_path, "INFO", scheduled_db_save_msg)
            
            return parsed_data
            
        except KeyboardInterrupt:
            # This exception might not be reliably caught in all async setups
            kb_interrupt_msg = "Processing interrupted by user (KeyboardInterrupt)."
            self.logger.info(kb_interrupt_msg)
            self._log_to_debug_file(debug_path, "INFO", kb_interrupt_msg)
            return self._get_empty_structure()
        except Exception as e:
            error_msg = f"Processing error in parse method: {str(e)}"
            self.logger.error(error_msg)
            self._log_to_debug_file(debug_path, "ERROR", error_msg, exc_info_tuple=sys.exc_info()) # Pass exc_info for traceback
            
            self.logger.exception("Full traceback (logged to console):") # Console gets full traceback via logger.exception
            # No need to log traceback twice to file, _log_to_debug_file above handles it if exc_info_tuple is passed
            return self._get_empty_structure()
        finally:
            if fileId:
                async with ResumeParser._processing_lock:
                    ResumeParser._active_processing_file_ids.discard(fileId)
                    final_discard_msg = f"Finally block: Discarded fileId {fileId} from _active_processing_file_ids."
                    self.logger.debug(final_discard_msg) # Usually debug for this
                    self._log_to_debug_file(debug_path, "DEBUG", final_discard_msg)

    def _log_to_debug_file(self, debug_path: str, level_str: str, message_str: str, exc_info_tuple=None):
        """Helper method to append formatted log messages to a specific debug file."""
        try:
            log_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3] + "Z"
            
            # Request ID is now handled by thread-local storage
            current_request_id = 'DEBUG_LOG'  # Simple identifier for debug logs
            logger_name = 'resume_parser'
            
            log_prefix = f"{log_timestamp} [{current_request_id}] [{level_str.upper()}] [{logger_name}] "
            
            full_log_message = log_prefix + message_str
            if exc_info_tuple:
                import traceback # Import locally as it's only used here
                formatted_exception_trace = "".join(traceback.format_exception(exc_info_tuple[0], exc_info_tuple[1], exc_info_tuple[2]))
                full_log_message += "\n" + formatted_exception_trace
        
            with open(debug_path, 'a', encoding='utf-8') as f_debug:
                f_debug.write(full_log_message + "\n")
        except Exception as e_log_write_fail:
            # Log to console if file logging fails
            self.logger.logger.error( # Use underlying logger to avoid recursion if adapter is problematic
                f"CRITICAL: Failed to write to debug log file {debug_path}. "
                f"Original log: [{level_str.upper()}] {message_str}. Error: {e_log_write_fail}"
            )

    async def _save_to_database_and_evaluate(self, parsed_data: Dict[str, Any], text: str, userId: str, background_tasks: BackgroundTasks, debug_path: str, companyId: Optional[str] = None, fileId: Optional[str] = None) -> None:
        """Save resume data to database and then evaluate it"""

        self.logger.info(f"Preparing to save parsed resume data to UserResume for user: {userId}")

        conn = None # Initialize conn to None
        try:
            conn = await asyncpg.connect(settings.database_url)

            # Check if fileId already exists before attempting to save
            if fileId:
                exists = await conn.fetchval('''
                    SELECT EXISTS (SELECT 1 FROM "UserResume" WHERE "fileId" = $1)
                ''', fileId)
                if exists:
                    # Log to console (already does) and to debug file
                    self.logger.info(f"File ID {fileId} already exists in UserResume for user {userId}. Skipping save and evaluation.")
                    # No need to close conn here, finally block will handle it
                    return # Exit the function


            # --- Robustly determine and structure resume_content_snake_case ---
            default_empty_resume_content = self._get_empty_structure()['resume']

            # Start with a deep copy of the default structure. This ensures all keys are present.
            resume_content_snake_case = copy.deepcopy(default_empty_resume_content)

            raw_llm_inner_content = None
            if isinstance(parsed_data, dict): # parsed_data is the full LLM output e.g. {"Resume": {...}}
                if 'Resume' in parsed_data and isinstance(parsed_data['Resume'], dict):
                    raw_llm_inner_content = parsed_data['Resume']
                elif 'resume' in parsed_data and isinstance(parsed_data['resume'], dict): # Should not happen from LLM, but good for fallback
                    raw_llm_inner_content = parsed_data['resume']
                elif 'standard_fields' in parsed_data: # Check if parsed_data itself is the inner content (unlikely for LLM)
                    raw_llm_inner_content = parsed_data
            
            if raw_llm_inner_content: # Check if there's actual content from LLM to process
                self.logger.info("Populating resume content from LLM data.")
                # Update standard_fields if present and valid in LLM output
                # Ensure standard_fields from LLM is a dictionary before trying to access its keys
                llm_standard_fields = raw_llm_inner_content.get('standard_fields')
                if isinstance(llm_standard_fields, dict):
                    # Update header if valid (merge LLM data into default header)
                    if isinstance(llm_standard_fields.get('header'), dict):
                        resume_content_snake_case['standard_fields']['header'].update(llm_standard_fields['header'])
                    
                    # Update summary if valid (merge LLM data into default summary)
                    if isinstance(llm_standard_fields.get('summary'), dict):
                        resume_content_snake_case['standard_fields']['summary'].update(llm_standard_fields['summary'])

                    # Replace experience if valid list from LLM, otherwise default empty list remains
                    if isinstance(llm_standard_fields.get('experience'), list):
                        resume_content_snake_case['standard_fields']['experience'] = llm_standard_fields['experience']
                    
                    # Replace education if valid list from LLM, otherwise default empty list remains
                    if isinstance(llm_standard_fields.get('education'), list):
                        resume_content_snake_case['standard_fields']['education'] = llm_standard_fields['education']

                    # Replace skills if valid list from LLM, otherwise default empty list remains
                    if isinstance(llm_standard_fields.get('skills'), list):
                        resume_content_snake_case['standard_fields']['skills'] = llm_standard_fields['skills']
                else:
                    self.logger.info("LLM output missing 'standard_fields' or 'standard_fields' is not a dict. Using default standard_fields from empty structure.")
                    # resume_content_snake_case['standard_fields'] is already the default from deepcopy

                # Update additional_sections if present and valid list in LLM output, otherwise default empty list remains
                llm_additional_sections = raw_llm_inner_content.get('additional_sections')
                if isinstance(llm_additional_sections, list):
                    resume_content_snake_case['additional_sections'] = llm_additional_sections
                # else: resume_content_snake_case['additional_sections'] is already default [] from deepcopy
            else:
                self.logger.warning(
                    "Could not find 'Resume' or 'resume' key in parsed_data, "
                    f"nor was parsed_data the inner content. Using full default structure. Parsed data type: {type(parsed_data)}"
                )
                # resume_content_snake_case is already the default from deepcopy
            # --- End of robust structure determination ---

            # For individual DB column extraction, use the now robust resume_content_snake_case
            standard_fields_for_columns = resume_content_snake_case.get('standard_fields', default_empty_resume_content['standard_fields']) # Should always be a dict
            header_data_for_columns = standard_fields_for_columns.get('header', {})
            summary_data_for_columns = standard_fields_for_columns.get('summary', {})

            # Process fields using helper functions
            raw_name = header_data_for_columns.get('name', '') # Ensure raw_name is always a string
            normalized_name = normalize_headers(raw_name)
            
            resume_username = generate_username(normalized_name)
            resume_name = safe_field(normalized_name) # Already normalized
            resume_email = safe_field(header_data_for_columns.get('email'))
            resume_phone = safe_field(header_data_for_columns.get('phone'))
            resume_website = safe_field(header_data_for_columns.get('website'))
            resume_xaccount = safe_field(header_data_for_columns.get('xaccount', header_data_for_columns.get('twitter')))
            resume_linkedin = safe_field(header_data_for_columns.get('linkedin'))
            resume_github = safe_field(header_data_for_columns.get('github'))
            resume_address = safe_field(header_data_for_columns.get('address'))
            # 'about' comes from 'shortAbout' in header or 'summary.content'
            resume_about = safe_field(header_data_for_columns.get('shortAbout', summary_data_for_columns.get('content')))

            # Encrypt fields
            encrypted_name = encrypt_to_buffer(resume_name)
            encrypted_about = encrypt_to_buffer(resume_about)
            encrypted_email = encrypt_to_buffer(resume_email)
            encrypted_phone = encrypt_to_buffer(resume_phone)
            encrypted_website = encrypt_to_buffer(resume_website)
            encrypted_xaccount = encrypt_to_buffer(resume_xaccount)
            encrypted_linkedin = encrypt_to_buffer(resume_linkedin)
            encrypted_github = encrypt_to_buffer(resume_github)
            encrypted_address = encrypt_to_buffer(resume_address)
            encrypted_file_content = encrypt_to_buffer(text) # rawText

            # 2. For the resumeData blob: Convert keys to camelCase and store this transformed version.
            resume_data_for_nextjs_camel_case = _snake_to_camel_case_dict_keys(resume_content_snake_case)
            encrypted_resume_data = encrypt_to_buffer(json.dumps(resume_data_for_nextjs_camel_case))

            user_resume_id = str(uuid.uuid4())

            await conn.execute('''
                INSERT INTO "UserResume" (
                    id, "userId", "companyId", "fileId", username,
                    name, about, email, phone, website, xaccount,
                    linkedin, github, address, "fileContent", "resumeData"
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
            ''', 
            user_resume_id, userId, companyId, fileId, resume_username, # Note: resume_username is not encrypted
            encrypted_name, encrypted_about, encrypted_email, encrypted_phone,
            encrypted_website, encrypted_xaccount, encrypted_linkedin, encrypted_github,
            encrypted_address, encrypted_file_content, encrypted_resume_data
            )
            self.logger.info(f"Resume data saved to UserResume for user: {userId}, resumeId: {user_resume_id}")

            # Evaluate resume in background ONLY if save was successful
            background_tasks.add_task(
                self._evaluate_resume,
                resume_data=parsed_data,
                userId=userId,
                resumeId=user_resume_id,
                background_tasks=background_tasks
                # If _evaluate_resume also needs to log to this specific file, pass debug_path here too
            )
            
        except asyncpg.PostgresError as db_pg_error: # Catch specific asyncpg errors first
            error_summary = (
                f"PostgreSQL error during save to UserResume: {str(db_pg_error)}\n"
                f"SQLSTATE: {getattr(db_pg_error, 'sqlstate', 'N/A (attribute missing)')}\n"
                f"Details: {getattr(db_pg_error, 'details', 'N/A (attribute missing)')}\n"
                f"Query: {getattr(db_pg_error, 'query', 'N/A (attribute missing)')}"
            )
            # Log to console
            self.logger.error(error_summary)
            self.logger.exception("Full traceback for UserResume save failure (PostgresError):")
            # Log to debug file
            self._log_to_debug_file(debug_path, "ERROR", error_summary, exc_info_tuple=sys.exc_info())

        except Exception as db_error: # General fallback
            error_summary = f"General error during save to UserResume table: {str(db_error)}"
            # Log to console
            self.logger.error(error_summary)
            self.logger.exception("Full traceback for UserResume save failure (General Exception):")
            # Log to debug file
            self._log_to_debug_file(debug_path, "ERROR", error_summary, exc_info_tuple=sys.exc_info())

        finally:
            if conn: # Ensure conn was assigned before trying to close
                await conn.close()

    async def _evaluate_resume(self, 
                               resume_data: Dict[str, Any], 
                               userId: str, 
                               resumeId: str, 
                               background_tasks: BackgroundTasks #, 
                               # debug_path: Optional[str] = None # Add if _evaluate_resume needs to log to the same file
                               ) -> None:
        """Background task to evaluate resume using ResumeEvaluator directly"""
        try:
            
            self.logger.info(f"Starting background resume evaluation for user: {userId}")
            
            # Create evaluator instance
            evaluator = ResumeEvaluator(self.ml_manager)
            
            # Evaluate the resume
            result = await evaluator.evaluate(resume_data, userId, resumeId=resumeId, background_tasks=background_tasks)
            
            self.logger.info(f"Resume evaluation completed for user: {userId}")
            self.logger.info(f"Resume evaluation: {result}")
            # if debug_path:
            #     self._log_to_debug_file(debug_path, "INFO", f"Resume evaluation completed for user {userId}. Result: {result}")
            
        except Exception as e:
            error_summary = f"Failed to evaluate resume: {str(e)}"
            # Log to console
            self.logger.error(error_summary)
            self.logger.exception("Full traceback for resume evaluation failure:")
            # if debug_path:
            #     self._log_to_debug_file(debug_path, "ERROR", error_summary, exc_info_tuple=sys.exc_info())


    # Previous: save data to UserResumeTemp table
    async def _save_to_temp_database(self, parsed_data: Dict[str, Any], text: str, userId: str, background_tasks: BackgroundTasks, companyId: Optional[str] = None, fileId: Optional[str] = None) -> None:
        """Save resume data to database and then evaluate it"""
        
        self.logger.info(f"Saving resume data to database for user: {userId, companyId, fileId}")
        
        try:
            conn = await asyncpg.connect(settings.database_url)
            
            # Convert parsed_data to string explicitly
            resume_data_str = json.dumps(parsed_data)
            
            # Generate a resumeId
            resumeId = str(uuid.uuid4())
            
            await conn.execute('''
                INSERT INTO "UserResumeTemp" (id, "userId", "companyId", "fileId", "rawText", "resumeData")
                VALUES ($1, $2, $3, $4, $5, $6)
            ''', 
            resumeId, 
            userId,
            companyId,
            fileId,
            text,
            resume_data_str
            )
            
            await conn.close()
            self.logger.info(f"Resume data saved to database for user: {userId}, resumeId: {resumeId}")        
            
        except Exception as db_error:
            self.logger.error(f"Failed to save resume to database: {str(db_error)}")

    def _get_empty_structure(self) -> Dict[str, Any]:
        """Return empty resume structure with all required fields"""
        return {
            'resume': {
                'standard_fields': {
                    'header': {
                        'name': '',
                        'email': '',
                        'phone': [],
                        'address': '',
                        'website': '',
                        'linkedin': '',
                        'github': '',
                        'xaccount': '',
                        'shortAbout': ''
                    },
                    'summary': {'content': ''},
                    'experience': [],
                    'education': [],
                    'skills': []
                },
                'additional_sections': [] # Ensure this is an array for consistency
            } # This is the inner content, matching default_empty_resume_content
        }
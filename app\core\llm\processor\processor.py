from typing import Dict, Any
import torch

from app.config.settings import settings  # Fix: import settings instance, not the module
from ...manager.ml_manager import <PERSON><PERSON><PERSON><PERSON>
from asyncio import TimeoutError, wait_for
from ....utils.logging_helpers import get_logger

logger = get_logger(__name__)

class LLMProcessor:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            try:
                self.ml_manager = MLManager()
                self.initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize LLMProcessor: {str(e)}")
                self.initialized = False
                raise RuntimeError(f"LLMProcessor initialization failed: {str(e)}")

    async def process(self, prompt: str, request_id: str, timeout: int = settings.processing_timeout) -> Dict[str, Any]:
        """Single entry point for all LLM processing"""        
        if not self.initialized:
            return {
                "request_id": request_id,
                "error": "LLMProcessor not properly initialized",
                "status": "error"
            }

        try:
            # Clear CUDA cache before processing
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            response = await wait_for(
                self._process_with_memory_management(prompt),
                timeout=timeout
            )
        
            return self._process_response(response, request_id)
        except TimeoutError:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            return {
                "request_id": request_id,
                "error": "Processing timeout",
                "status": "error"
            }
        except Exception as e:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            logger.error(f"Processing error: {str(e)}")
            return {
                "request_id": request_id,
                "error": str(e),
                "status": "error"
            }

    def _process_response(self, response: str, request_id: str) -> Dict[str, Any]:
        try:
            return {
                "request_id": request_id,
                "response": response,
                "status": "success"
            }
        except Exception as e:
            logger.error(f"Response processing error: {str(e)}")
            return {
                "request_id": request_id,
                "error": str(e),
                "status": "error"
            }

    async def _process_with_memory_management(self, prompt: str):
        """Process with explicit memory management"""
        try:
            return await self.ml_manager.generate_response(prompt)
        finally:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

    async def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'ml_manager'):
            await self.ml_manager.shutdown()  # Changed from cleanup() to shutdown()
        self.initialized = False

JOB_TO_RESUMES_PROMPT = """You are an expert job matching system. Your task is to analyze how well the provided resumes match the requirements of the given job posting.

**Input:**

The job posting text is enclosed in triple angle brackets:

<<<
{job_text}
>>>

The resume texts are enclosed in triple angle brackets:

<<<
{resumes_text}
>>>


**Matching Rules:**

1.  **Evidence-Based Matching:** Determine matches based on CLEAR evidence of matching skills and experience found within the resumes.
2.  **Specific Matches:** Look for SPECIFIC matching keywords and experiences, not general or vague similarities.
3.  **Comprehensive Analysis:** Consider both explicit requirements stated in the job posting and implicit needs that can be inferred.
4.  **Key Factors:** Pay close attention to required skills, experience level, and relevant domain knowledge.
5.  **Strict Scoring:** Apply strict scoring criteria. Only assign high scores (80+) when there are clear, specific, and substantial matches.

**Analysis Focus:**

For each resume, analyze the match focusing on these three aspects:

1.  **Skills Match:** How well do the candidate's specific skills align with the skills required in the job posting?
2.  **Experience Alignment:** How well does the candidate's experience level and type align with the experience needed for the job?
3.  **Education Fit:** Does the candidate's education meet the educational requirements specified in the job posting?

**Output Instructions:**

1.  **JSON Format:** Return ONLY a valid JSON object. Do not include any introductory or explanatory text before or after the JSON. The JSON must adhere to the following structure:

    ```json
    {{
      "matches": [
        {{
          "resume_id": "string",
            "match_scores": {{
                "overall_score": (0-100),
                "skills_match": (0-100),
                "experience_alignment": (0-100),
                "education_fit": (0-100)
            }}
          "match_analysis": "Detailed explanation of why scores were given"
          "candidate_strengths": "List of SPECIFIC matching strengths (only include truly matching items)"
          "matching_skills": ["skill1", "skill2", ...],
          "missing_requirements": ["req1", "req2", ...],
          "experience_relevance": "high|medium|low",
          "recommendations": [
            "specific recommendation 1",
            "specific recommendation 2"
          ]
        }}
      ]
    }}
    ```

    * `resume_id`:  A unique identifier for each resume.
    * `match_score`: A numerical score (0-100) representing the overall match between the resume and the job posting.
    * `matching_skills`: A list of specific skills from the resume that directly match skills required in the job posting.
    * `missing_requirements`: A list of key requirements from the job posting that are not clearly met by the resume.
    * `experience_relevance`: An assessment of the experience relevance ("high", "medium", or "low").
    * `match_analysis`: Detailed explanation of why scores were given.
    * `candidate_strengths`: List of SPECIFIC matching strengths (only include truly matching items).

2.  **JSON Validity:**

    * The output MUST be a valid JSON object.
    * Ensure all arrays are properly closed with `]` and all objects with `}}`.
    * Ensure correct nesting of arrays and objects.
    * Do not include trailing commas in arrays or objects.
    * Ensure all `match_scores` are within the range of 0 to 100 (inclusive).
    * Ensure each `resume_id` accurately corresponds to the resumes provided in the input.

**Scoring Rules:**

<<<
{scoring_rules_section}
>>>

**Output:**

Return ONLY the valid JSON object.
"""

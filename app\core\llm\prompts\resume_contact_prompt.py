RESUME_CONTACT_PROMPT = """You are an expert resume reader. Given the text below from the top of a resume, extract the candidate's contact information in structured JSON format.

**Input:**

The resume text to parse is enclosed in triple angle brackets:

<<<
{resume_text}
>>>

**Output Instructions:**

1. Extract **exactly** the following keys:
- full name
- email address
- phone number
- current job title or headline (if available)
- location (e.g. city, state, country)
- LinkedIn URL (if available)
- GitHub URL (if available)
- personal website or portfolio URL (if available)
- Twitter or X account (if included)
- summary or short about bio (if available, usually appears near the top of the resume)

Return `null` for any fields that are not present. Do not add extra information.

Respond with a **valid JSON object** only — no markdown, no explanation, no preamble.

    ```json
    {{
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "************",
        "title": "Marketing Assistant",
        "address": "123 Main St, Los Angeles, CA, United States",
        "linkedin": "https://linkedin.com/in/emmawilson",
        "github": null,
        "website": "http://emmawilson.com",
        "xaccount": "@emmawilson"
        "summary": "Passionate Marketing Assistant with a love for exclusive customer awareness and engagement strategies incorporating online and offline marketing tools to promise holistic reach-out campaigns."
    }}
    ```
"""

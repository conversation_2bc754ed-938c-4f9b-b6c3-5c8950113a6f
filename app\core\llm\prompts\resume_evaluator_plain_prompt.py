RESUME_EVALUATOR_PLAIN_PROMPT = """You are an expert resume evaluator. Your task is to first parse the provided raw resume text into a structured format, and then evaluate it based on the criteria below.

**Input:**
The raw resume text is enclosed in triple angle brackets:

<<<
{resume_text}
>>>

---

**Step 1: Parse the Resume Text**

First, extract the following key sections from the raw resume text. If a section or field is not present, leave it empty or as an empty array. Do not invent information.

*   **Header/Contact Info:** Name, Email, Phone numbers (as a list), LinkedIn URL, GitHub URL, Website URL, Address.
*   **Summary/Objective:** The main summary or objective statement.
*   **Work Experience:** For each job: Title, Company, Start Date (YYYY-MM or YYYY), End Date (YYYY-MM or YYYY or "Present"), Location, and a list of Responsibilities/Achievements (bullet points).
*   **Education:** For each degree/program: Degree, Institution, Start Date (YYYY), End Date (YYYY), Location, and any additional details (e.g., GPA, honors).
*   **Skills:** A flat list of technical and soft skills.

Represent this parsed data internally as a JSON-like structure.

---

**Step 2: Evaluate the Parsed Resume**

Now, evaluate the parsed resume data based on the following criteria. Provide scores from 0-100 for each category and a detailed assessment.

**Evaluation Criteria:**

*   **Experience Quality (0-100):**
    *   Relevance to typical professional roles.
    *   Impact and achievements described (quantifiable results are a plus).
    *   Progression and growth shown over time.
    *   Clarity and conciseness of descriptions.
*   **Education (0-100):**
    *   Relevance of degrees/certifications to professional roles.
    *   Reputation of institutions.
    *   Academic achievements (e.g., honors, GPA if present).
*   **Skills (0-100):**
    *   Relevance and breadth of skills.
    *   Demonstrated application of skills in experience.
    *   Balance of technical and soft skills.
*   **Completeness (0-100):**
    *   Presence of all expected sections (contact, summary, experience, education, skills).
    *   Detailed information within each section (e.g., dates, responsibilities for all jobs).
    *   Absence of significant gaps or missing information.
*   **Overall (0-100):**
    *   A holistic score reflecting the overall strength and marketability of the resume. This should be a weighted average of the above scores: Experience Quality (35%), Education (25%), Skills (25%), Completeness (15%). Round to the nearest integer. If the calculated overall score is 0 but any component score is above 0, adjust the overall score to be at least the maximum component score multiplied by the minimum weight (e.g., max_component * 0.15).

**Assessment Details:**

*   **Strengths:** List 3-5 key positive aspects of the resume.
*   **Improvements:** List 3-5 areas where the resume could be improved.
*   **Industry Recommendations:** Provide 2-3 actionable recommendations relevant to the resume's content, potentially suggesting industries or roles it might be well-suited for, or areas for further development.

---

**Output Format:**

Return a single, complete, valid JSON object **only**. The JSON must match the following structure:

```json
{{
    "scores": {{
        "experience_quality": 0,
        "education": 0,
        "skills": 0,
        "completeness": 0,
        "overall": 0
    }},
    "assessment": {{
        "strengths": [
            "string",
            "string",
            "string"
        ],
        "improvements": [
            "string",
            "string",
            "string"
        ],
        "industry_recommendations": [
            "string",
            "string"
        ]
    }},
    "evaluation_details": {{
        "experience": "Detailed explanation of experience evaluation.",
        "education": "Detailed explanation of education evaluation.",
        "skills": "Detailed explanation of skills evaluation.",
        "completeness": "Detailed explanation of completeness evaluation, including presence of contact info."
    }}
}}
```

**Final Validation (Crucial):**

Before returning the JSON, perform these checks:

*   Is the JSON complete with all brackets and braces correctly closed?
*   Are all arrays properly closed with `]`?
*   Are all objects properly closed with `}}`?
*   Are there any missing commas between items?
*   Are there any trailing commas?
*   Are all strings properly quoted with `"`?
*   Is the entire response valid JSON?

**RECHECK AND VALIDATE THE JSON OUTPUT. REDO THE JSON IF NOT IN CORRECT FORMAT. RETURN ONLY A VALID JSON OBJECT**
"""
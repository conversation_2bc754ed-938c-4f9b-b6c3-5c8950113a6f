# GPU management
import torch
import os
from typing import List, Dict
from dataclasses import dataclass
from threading import Lock
from ...utils.logging_helpers import get_logger

@dataclass
class GPUStats:
    """GPU statistics"""
    device_id: int
    memory_total: int
    memory_used: int
    memory_free: int
    utilization: float = 0.0
    name: str = ""  # Added to track GPU model name

class GPUManager:
    """
    Manages GPU resources and allocation with cloud deployment support
    """
    _instance = None
    _lock = Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.logger = get_logger("ml_processor.gpu")
            self.initialized = True
            self.is_cloud = self._check_cloud_environment()

    def _check_cloud_environment(self) -> bool:
        """Check if running in cloud environment"""
        return bool(os.getenv('AZURE_CONTAINER_NAME') or os.getenv('WEBSITE_INSTANCE_ID'))

    def get_gpu_stats(self) -> List[GPUStats]:
        """
        Get detailed statistics for all available GPUs
        
        Returns:
            List[GPUStats]: List of GPU statistics
        """
        stats = []
        if not torch.cuda.is_available():
            return stats

        for i in range(torch.cuda.device_count()):
            with torch.cuda.device(i):
                props = torch.cuda.get_device_properties(i)
                total = props.total_memory
                memory = torch.cuda.memory_stats()
                used = memory.get("allocated_bytes.all.current", 0)
                free = total - used
                
                try:
                    util = torch.cuda.utilization(i)
                except (AttributeError, ModuleNotFoundError):
                    util = 0.0
                    
                stats.append(GPUStats(
                    device_id=i,
                    memory_total=total,
                    memory_used=used,
                    memory_free=free,
                    utilization=util,
                    name=props.name
                ))
                
                self.logger.debug(
                    f"GPU {i} ({props.name}): "
                    f"Free: {free/1024**3:.2f}GB, "
                    f"Used: {used/1024**3:.2f}GB, "
                    f"Total: {total/1024**3:.2f}GB, "
                    f"Util: {util}%"
                )
        return stats

    def get_memory_map(self, memory_ratio: float = 0.90) -> Dict[int, str]:
        """
        Get memory map with cloud environment support
        """
        if self.is_cloud:
            # Check for Azure GPU configuration
            gpu_memory = os.getenv('GPU_MEMORY_GB')
            if gpu_memory:
                try:
                    memory = int(float(gpu_memory) * memory_ratio)
                    return {0: f"{memory}GiB"}
                except ValueError:
                    self.logger.warning("Invalid GPU_MEMORY_GB environment variable")
            return {}

        # Original local GPU logic
        memory_map = {}
        stats = self.get_gpu_stats()
        
        for stat in stats:
            allocated_memory = int((stat.memory_free / 1024**3) * memory_ratio)
            memory_map[stat.device_id] = f"{allocated_memory}GiB"
            
        return memory_map

    def get_available_devices(self) -> List[torch.device]:
        """
        Get list of all available GPU devices
        
        Returns:
            List[torch.device]: List of available devices
        """
        devices = []
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                devices.append(torch.device(f"cuda:{i}"))
        if not devices:
            devices.append(torch.device("cpu"))
        return devices

    def get_recommended_device_map(self) -> str:
        """
        Get recommended device map setting for model loading with cloud support
        
        Returns:
            str: Appropriate device map setting
        """
        if self.is_cloud:
            # Check for specific Azure GPU environment variables
            if os.getenv('GPU_COUNT'):
                return "auto"
            return "cpu"
            
        if not torch.cuda.is_available():
            return "cpu"
        
        if torch.cuda.device_count() > 1:
            return "auto"
        
        return f"cuda:{self._select_best_gpu()}"

    def _select_best_gpu(self) -> int:
        """
        Select GPU with most available memory
        
        Returns:
            int: GPU device ID
        """
        stats = self.get_gpu_stats()
        if not stats:
            return 0

        # Consider both memory and utilization
        best_gpu = max(stats, key=lambda x: (x.memory_free, 100 - x.utilization))
        return best_gpu.device_id

    def print_gpu_info(self):
        """Print GPU information with cloud support"""
        if self.is_cloud:
            self.logger.info("=== Cloud Environment GPU Information ===")
            gpu_count = os.getenv('GPU_COUNT', '0')
            gpu_memory = os.getenv('GPU_MEMORY_GB', 'N/A')
            self.logger.info(f"Cloud GPU Count: {gpu_count}")
            self.logger.info(f"Cloud GPU Memory: {gpu_memory}GB")
            return

        # Original local GPU logic
        if not torch.cuda.is_available():
            self.logger.info("No GPUs available")
            return

        stats = self.get_gpu_stats()
        self.logger.info("=== Local GPU Information ===")
        for stat in stats:
            self.logger.info(
                f"GPU {stat.device_id} - {stat.name}\n"
                f"  Memory: {stat.memory_total/1024**3:.2f}GB total, "
                f"{stat.memory_free/1024**3:.2f}GB free\n"
                f"  Utilization: {stat.utilization}%"
            )

    def cleanup(self):
        """
        Clean up GPU resources and memory
        """
        try:
            if torch.cuda.is_available():
                # Empty CUDA cache
                torch.cuda.empty_cache()
                
                # Reset peak memory stats
                torch.cuda.reset_peak_memory_stats()
                
                # Log memory status after cleanup
                stats = self.get_gpu_stats()
                for stat in stats:
                    self.logger.debug(
                        f"GPU {stat.device_id} after cleanup: "
                        f"Free: {stat.memory_free/1024**3:.2f}GB, "
                        f"Used: {stat.memory_used/1024**3:.2f}GB"
                    )
        except Exception as e:
            self.logger.error(f"Error during GPU cleanup: {e}")
            raise



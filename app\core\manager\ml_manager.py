# ML operations manager
import re
from ...config.settings import settings
from .model_singleton import Model<PERSON><PERSON>leton
from asyncio import Lock
from ...utils.logging_helpers import get_logger

logger = get_logger(__name__)

class MLManager:
    def __init__(self):
        logger.info("Initializing ML Manager...")
        self.model_singleton = ModelSingleton.get_instance()
        self._lock = Lock()
        logger.info("ML Manager initialized successfully")

    async def generate_response(self, prompt: str) -> str:
        """Generate response from the model"""
        async with self._lock:
            return await self._generate_response_impl(prompt)

    async def _generate_response_impl(self, prompt: str) -> str:
        """Generate response from the model"""
        try:
            logger.info("Starting response generation")
            formatted_prompt = f"<s>[INST] {prompt} [/INST]"
            
            logger.debug(f"Tokenizing prompt")
            inputs = self.model_singleton.tokenizer(formatted_prompt, return_tensors="pt").to(self.model_singleton.model.device)
            
            # Log tokenizer inputs
            logger.debug(f"Input IDs shape: {inputs.input_ids.shape}")
            logger.debug(f"Attention mask shape: {inputs.attention_mask.shape}")
            
            # Base generation parameters
            generation_params = {
                "input_ids": inputs.input_ids,
                "attention_mask": inputs.attention_mask,
                "do_sample": settings.do_sample,
                "max_new_tokens": settings.max_new_tokens,
                "pad_token_id": self.model_singleton.tokenizer.pad_token_id,
                "eos_token_id": self.model_singleton.tokenizer.eos_token_id,
                # Explicitly set these to None when do_sample is False
                "top_k": None if not settings.do_sample else settings.top_k,
                "top_p": None if not settings.do_sample else settings.top_p
            }
            
            logger.debug(f"Generation parameters: {generation_params}")
            
            # Only add temperature when do_sample is True
            if settings.do_sample:
                generation_params.update({
                    "temperature": settings.temperature
                })
                
            logger.info("Starting model generation")
            outputs = self.model_singleton.model.generate(
                **generation_params,
                return_dict_in_generate=True,
                output_scores=False
            )
            
            logger.debug(f"Output sequences shape: {outputs.sequences.shape}")
            
            # Decode only the generated part (excluding the input prompt)
            input_length = inputs.input_ids.shape[1]
            generated_tokens = outputs.sequences[:, input_length:]
            
            raw_output = self.model_singleton.tokenizer.decode(generated_tokens[0], skip_special_tokens=True)
            logger.info(f"Raw output length: {len(raw_output)}")
            logger.debug(f"Raw output: {raw_output[:200]}...")  # Log first 200 chars
            
            cleaned_output = _extract_json(raw_output)
            logger.info(f"Cleaned output length: {len(cleaned_output)}")
            logger.debug(f"Cleaned output: {cleaned_output[:200]}...")  # Log first 200 chars

            return cleaned_output
        
        except Exception as e:
            logger.error(f"Error in _generate_response_impl: {str(e)}", exc_info=True)
            raise

    async def shutdown(self):
        """Cleanup resources during shutdown"""
        try:
            if hasattr(self, 'model_singleton') and self.model_singleton:
                # Clean up GPU resources first
                self.model_singleton.gpu_manager.cleanup()
                # Release model resources
                self.model_singleton.model = None
                self.model_singleton.tokenizer = None
                logger.info("ML Manager shutdown completed successfully")
            else:
                logger.info("ML Manager shutdown: model_singleton not found or already cleaned up.")
        except Exception as e:
            logger.error(f"Error during MLManager shutdown: {e}")
            raise

    def __del__(self):
        """Cleanup when the manager is destroyed"""
        try:
            if hasattr(self, 'model_singleton') and self.model_singleton:
                # Clean up GPU resources first
                self.model_singleton.gpu_manager.cleanup()
                # Release model resources
                self.model_singleton.model = None
                self.model_singleton.tokenizer = None
                logger.info("ML Manager cleaned up successfully in __del__")
            else:
                logger.info("ML Manager __del__: model_singleton not found or already cleaned up.")
        except Exception as e:
            logger.error(f"Error during ML Manager cleanup: {e}")
            
def _extract_json(raw_output: str) -> str:
    """Try to extract the first JSON object from the LLM output."""
    match = re.search(r'{.*}', raw_output, re.DOTALL)
    if match:
        return match.group(0)
    return raw_output  # fallback

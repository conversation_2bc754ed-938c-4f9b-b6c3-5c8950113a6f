import torch
from transformers.models.auto.tokenization_auto import AutoTokenizer
from transformers.models.auto.modeling_auto import AutoModelForCausalLM
from transformers.utils.quantization_config import BitsAndBytesConfig
from ...config.settings import settings
from logging import getLogger
from .gpu_manager import <PERSON>UManager
from threading import Lock
from typing import Optional, Any

logger = getLogger(__name__)

class ModelSingleton:
    _instance: Optional['ModelSingleton'] = None
    _lock: Lock = Lock()
    
    # Define class attributes
    model: Any
    tokenizer: Any
    gpu_manager: GPUManager
    initialized: bool

    def __new__(cls) -> 'ModelSingleton':
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    instance = super().__new__(cls)
                    # Initialize instance attributes
                    instance.model = None
                    instance.tokenizer = None
                    instance.gpu_manager = GPUManager()
                    instance.initialized = False
                    cls._instance = instance
        return cls._instance

    def __init__(self) -> None:
        # Only initialize once
        if not self.initialized:
            self.initialized = True

    @classmethod
    def get_instance(cls) -> 'ModelSingleton':
        instance = cls()
        if not instance.model:
            instance._initialize()
        return instance

    def _initialize(self) -> None:
        if self.model is None:
            logger.info("Initializing ML resources...")
            self.model = self._init_model()
            self.tokenizer = self._init_tokenizer()
            logger.info("ML resources initialized successfully")

    def _init_model(self):
        try:
            logger.info(f"Loading model {settings.ai_model}")
            
            # Configure quantization
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Print GPU information before loading
            self.gpu_manager.print_gpu_info()
            
            # Get memory map for all GPUs
            max_memory = self.gpu_manager.get_memory_map(memory_ratio=0.90)
            
            # Get recommended device map
            device_map = self.gpu_manager.get_recommended_device_map()
            logger.info(f"Using device map: {device_map}")
            
            model = AutoModelForCausalLM.from_pretrained(
                settings.ai_model,
                token=settings.hf_token if settings.hf_token else None,
                device_map=device_map,
                max_memory=max_memory if max_memory else None,
                torch_dtype=torch.float16,
                quantization_config=quantization_config,
                low_cpu_mem_usage=True,
                trust_remote_code=True
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Failed to initialize model: {e}")
            if torch.cuda.is_available():
                stats = self.gpu_manager.get_gpu_stats()
                for stat in stats:
                    logger.error(f"GPU {stat.device_id} memory: Total={stat.memory_total/1024**3:.2f}GB, "
                               f"Free={stat.memory_free/1024**3:.2f}GB, "
                               f"Used={stat.memory_used/1024**3:.2f}GB, "
                               f"Utilization={stat.utilization}%")
            raise

    def _init_tokenizer(self):
        try:
            logger.info(f"Loading tokenizer for {settings.ai_model}")
            return AutoTokenizer.from_pretrained(
                settings.ai_model,
                token=settings.hf_token if settings.hf_token else None,
                trust_remote_code=True
            )
        except Exception as e:
            logger.error(f"Failed to initialize tokenizer: {e}")
            raise





from typing import Optional

class MLProcessingError(Exception):
    """Base exception for ML processing errors"""
    def __init__(self, message: str, details: Optional[dict] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)

class ModelLoadError(MLProcessingError):
    """Raised when model loading fails"""
    pass

class ProcessingTimeout(MLProcessingError):
    """Raised when processing exceeds timeout"""
    pass

class InvalidInputError(MLProcessingError):
    """Raised when input validation fails"""
    pass

class TokenizationError(MLProcessingError):
    """Raised when text tokenization fails"""
    pass
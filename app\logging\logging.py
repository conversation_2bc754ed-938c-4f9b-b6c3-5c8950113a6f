import logging
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler
from typing import Dict, Optional

class CustomFormatter(logging.Formatter):
    """Custom formatter with colors for console output"""
    
    COLORS = {
        'DEBUG': '\033[0;36m',    # Cyan
        'INFO': '\033[0;32m',     # Green
        'WARNING': '\033[0;33m',  # Yellow
        'ERROR': '\033[0;31m',    # Red
        'CRITICAL': '\033[0;35m', # Purple
        'RESET': '\033[0m',       # Reset
    }

    def format(self, record):
        # Add color and reset attributes to the record
        record.color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.reset = self.COLORS['RESET']
        return super().format(record)

def create_logger(name: str, log_dir: Path, log_level: int) -> logging.Logger:
    """Create a logger instance with both file and console handlers"""
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatters with a default request_id if not present
    class RequestFormatter(logging.Formatter):
        def format(self, record):
            if not hasattr(record, 'request_id'):
                record.request_id = 'NO_REQ_ID'
            return super().format(record)
    
    # Create formatters
    detailed_formatter = RequestFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Use CustomFormatter for console output
    console_formatter = CustomFormatter(
        '%(color)s%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] %(message)s%(reset)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create and configure TimedRotatingFileHandler
    log_file = log_dir / f"{name}.log"
    file_handler = TimedRotatingFileHandler(
        filename=log_file,
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(detailed_formatter)
    file_handler.setLevel(log_level)
    
    # Create console handler with color formatting
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)  # Use the color formatter
    console_handler.setLevel(log_level)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # Ensure logger propagates
    logger.propagate = False
    
    return logger

def setup_logging(log_dir: Path, log_level: int) -> dict:
    """Setup logging configuration"""
    
    log_dir.mkdir(parents=True, exist_ok=True)

    loggers = {
        'ml_processor': create_logger('ml_processor', log_dir, log_level),
        'evaluate_resume': create_logger('evaluate_resume', log_dir, log_level),
        'parse_resume': create_logger('parse_resume', log_dir, log_level),
        'parse_job': create_logger('parse_job', log_dir, log_level),
        'match_job_to_resumes': create_logger('match_job_to_resumes', log_dir, log_level),
        'match_resume_to_jobs': create_logger('match_resume_to_jobs', log_dir, log_level),
        'resume_parser': create_logger('resume_parser', log_dir, log_level),
        'job_parser': create_logger('job_parser', log_dir, log_level),
        'resume_evaluator': create_logger('resume_evaluator', log_dir, log_level),
        'llm_processor': create_logger('llm_processor', log_dir, log_level),
        'parse_contact': create_logger('parse_contact', log_dir, log_level),
        'evaluate_resume_plain': create_logger('evaluate_resume_plain', log_dir, log_level),
        'job__parser_V2': create_logger('job__parser_V2', log_dir, log_level),
    }

    return loggers

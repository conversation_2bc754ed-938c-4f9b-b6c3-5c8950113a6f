import torch
import time
import asyncio
import logging
from fastapi import FastAPI, Request, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
from typing import Optional
from app.core.llm.evaluator.evaluate_resume import ResumeEvaluator
from app.core.llm.evaluator.evaluate_resume_plain import ResumeEvaluatorPlain
from app.core.llm.parser.job_parser_v2 import JobParserV2
from app.core.llm.parser.job_parser import JobParser
from app.core.llm.parser.resume_contact_parser import ResumeContactInfoParser
from app.core.llm.parser.resume_parser import ResumeParser
from .schemas.request import ContactParseRequest, JobHeaderParseRequest, ResumeEvalRequest, ResumeEvalPlainRequest, DocumentParseRequest, ResumeToJobsRequest, JobToResumesRequest
from .schemas.response import ProcessingResponse, ParsedDocumentResponse, MatchingResponse
from .middleware.tracking import RequestTrackingMiddleware
from .core.manager.ml_manager import MLManager
from .logging.exceptions import MLProcessingError
from .config.settings import settings
from .utils.text_extractor import DocumentExtractor
from .core.llm.matcher.resume_to_jobs import ResumeToJobsMatcher
from .core.llm.matcher.job_to_resumes import JobToResumesMatcher
from .utils.logging_helpers import get_logger
from datetime import datetime, timezone
from pathlib import Path
import bitsandbytes as bnb

# Configure basic logging to show output in terminal with colors
class ColoredRequestIdFormatter(logging.Formatter):
    """Custom formatter that adds colors and handles missing request_id fields"""

    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m',      # Reset
        'BOLD': '\033[1m',       # Bold
        'DIM': '\033[2m',        # Dim
        'BLUE': '\033[34m',      # Blue
        'GRAY': '\033[90m',      # Gray
    }

    def format(self, record):
        # Add request_id if missing
        if not hasattr(record, 'request_id'):
            record.request_id = 'NO_REQ_ID'

        # Get color for log level
        level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])

        # Format timestamp in gray
        timestamp = self.formatTime(record, self.datefmt)
        colored_timestamp = f"{self.COLORS['GRAY']}{timestamp}{self.COLORS['RESET']}"

        # Format logger name in blue
        colored_name = f"{self.COLORS['BLUE']}{record.name}{self.COLORS['RESET']}"

        # Format level with appropriate color and bold
        colored_level = f"{self.COLORS['BOLD']}{level_color}{record.levelname}{self.COLORS['RESET']}"

        # Format request ID in dim style (using getattr to avoid type checker warnings)
        request_id = getattr(record, 'request_id', 'NO_REQ_ID')
        colored_request_id = f"{self.COLORS['DIM']}[{request_id}]{self.COLORS['RESET']}"

        # Format message with level color for important messages
        message = record.getMessage()
        if record.levelname in ['ERROR', 'CRITICAL']:
            colored_message = f"{level_color}{message}{self.COLORS['RESET']}"
        elif record.levelname == 'WARNING':
            colored_message = f"{level_color}{message}{self.COLORS['RESET']}"
        elif '===' in message:
            # Highlight section headers with bold green
            colored_message = f"{self.COLORS['BOLD']}{self.COLORS['INFO']}{message}{self.COLORS['RESET']}"
        elif any(keyword in message for keyword in ['Model:', 'Parameters:', 'Device:', 'Memory Usage:', 'Quantization:']):
            # Highlight important configuration messages with bold
            colored_message = f"{self.COLORS['BOLD']}{message}{self.COLORS['RESET']}"
        elif 'loaded successfully' in message or 'initialized successfully' in message:
            # Highlight success messages
            colored_message = f"{self.COLORS['BOLD']}{self.COLORS['INFO']}{message}{self.COLORS['RESET']}"
        else:
            colored_message = message

        # Combine all parts
        return f"{colored_timestamp} - {colored_name} - {colored_level} - {colored_request_id} {colored_message}"

# Set up console handler with custom colored formatter
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = ColoredRequestIdFormatter(datefmt='%Y-%m-%d %H:%M:%S')
console_handler.setFormatter(formatter)

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
root_logger.addHandler(console_handler)

# Setup logging with new logging helper
logger = get_logger(__name__)

# Global ML manager instance
ml_manager: Optional[MLManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global ml_manager
    # Use the global logger from logging_helpers
    logger.info("Starting up ML service...")
    try:
        logger.info(f"PyTorch version: {torch.__version__}")
        logger.info(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            # logger.info(f"CUDA version PyTorch is using: {torch.version.cuda}") # This is important!
            logger.info(f"Number of GPUs: {torch.cuda.device_count()}")
            logger.info(f"Current CUDA device: {torch.cuda.current_device()}")
            logger.info(f"Device name: {torch.cuda.get_device_name(torch.cuda.current_device())}")
        else:
            print("PyTorch cannot find CUDA. This needs to be fixed first.")
            
        logger.info(f"BitsAndBytes version: {bnb.__version__}")

        logger.info(f"Initializing with model: {settings.ai_model}")
        ml_manager = MLManager()
        
        # Log model configuration
        model = ml_manager.model_singleton.model
        model_size = sum(p.numel() for p in model.parameters()) / 1e9
        logger.info("=== Model Configuration ===")
        logger.info(f"Model: {settings.ai_model}")
        logger.info(f"Parameters: {model_size:.2f}B")
        logger.info(f"Device: {model.device}")
        logger.info(f"Memory Usage: {torch.cuda.max_memory_allocated()/1024**3:.2f}GB")
        logger.info(f"Quantization: 4-bit (nf4)")
        
        logger.info("ML models loaded successfully")
        yield
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down ML service...")
        try:
            if ml_manager:
                await cleanup_ml_resources()
                logger.info("ML resources cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

async def cleanup_ml_resources():
    """Cleanup ML resources in a controlled manner"""
    global ml_manager
    # Use the global logger from logging_helpers
    try:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        if ml_manager:
            await ml_manager.shutdown()
            del ml_manager
            ml_manager = None
    except Exception as e:
        logger.error(f"Error during ML resource cleanup: {e}")
        raise

# Initialize FastAPI app with lifespan
app = FastAPI(
    title=settings.app_name,
    description="ML Processing Service for Resume and Job Matching",
    version="1.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(RequestTrackingMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.exception_handler(MLProcessingError)
async def ml_processing_exception_handler(request: Request, exc: MLProcessingError):
    response_data = ProcessingResponse(
        request_id=request.state.request_id,
        status="error",
        error=exc.message,
        processing_time=time.time() - request.state.start_time,
        timestamp=datetime.now(timezone.utc)  
    )
    
    return JSONResponse(
        status_code=500,
        content=response_data.model_dump(mode='json')
    )

@app.post("/evaluate/resume")
async def evaluate_resume(
    request: Request,
    eval_request: ResumeEvalRequest,
    background_tasks: BackgroundTasks
) -> JSONResponse:
    """Evaluate a resume and provide detailed analysis"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting resume evaluation - Request ID: {request.state.request_id}, User ID: {eval_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        evaluator = ResumeEvaluator(ml_manager)
        
        # Add timeout to the evaluation
        result = await asyncio.wait_for(
            evaluator.evaluate(eval_request.resume_data, userId=eval_request.userId, resumeId=eval_request.resumeId, background_tasks=background_tasks),
            timeout=settings.processing_timeout
        )
        
        # Add cleanup to background tasks
        background_tasks.add_task(cleanup_resources)
        
        if result.get("status") == "error":
            raise MLProcessingError(result.get("error", "Unknown error during evaluation"))
            
        # Add userId to result
        result["userId"] = eval_request.userId
        result["resumeId"] = eval_request.resumeId
        
        # Convert datetime to string in the result before returning
        if "timestamp" in result:
            result["timestamp"] = result["timestamp"].isoformat()
            
        logger.info(f"Resume evaluation completed successfully - Request ID: {request.state.request_id}")
        return JSONResponse(content=result)  # Return as JSONResponse
        
    except asyncio.TimeoutError:
        logger.error("Resume evaluation timed out")
        raise MLProcessingError("Evaluation timed out")
    except Exception as e:
        logger.error(f"Error during resume evaluation: {e}")
        raise MLProcessingError(str(e))
    finally:
        # Ensure cleanup happens even if there's an error
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

async def cleanup_resources():
    """Background cleanup of resources after request processing"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

@app.post("/evaluate/resume_plain")
async def evaluate_resume_plain(
    request: Request,
    eval_request: ResumeEvalPlainRequest,
    background_tasks: BackgroundTasks
) -> JSONResponse:
    """Evaluate a resume from plain text and provide detailed analysis"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting plain text resume evaluation - Request ID: {request.state.request_id}, User ID: {eval_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        evaluator = ResumeEvaluatorPlain(ml_manager) 
        
        # Add timeout to the evaluation
        result = await asyncio.wait_for(
            evaluator.evaluate(eval_request.resume_text, userId=eval_request.userId, resumeId=eval_request.resumeId, background_tasks=background_tasks),
            timeout=settings.processing_timeout
        )
        
        # Add cleanup to background tasks
        background_tasks.add_task(cleanup_resources)
        
        if result.get("status") == "error":
            raise MLProcessingError(result.get("error", "Unknown error during evaluation"))
            
        # Add userId to result
        result["userId"] = eval_request.userId
        result["resumeId"] = eval_request.resumeId
        
        # Convert datetime to string in the result before returning
        if "timestamp" in result:
            result["timestamp"] = result["timestamp"].isoformat()
            
        logger.info(f"Plain text resume evaluation completed successfully - Request ID: {request.state.request_id}")
        return JSONResponse(content=result)  # Return as JSONResponse
        
    except asyncio.TimeoutError:
        logger.error("Plain text resume evaluation timed out")
        raise MLProcessingError("Evaluation timed out")
    except Exception as e:
        logger.error(f"Error during plain text resume evaluation: {e}")
        raise MLProcessingError(str(e))
    finally:
        # Ensure cleanup happens even if there's an error
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

@app.post("/parse/resume")
async def parse_resume(
    request: Request,
    parse_request: DocumentParseRequest,
    background_tasks: BackgroundTasks
) -> ParsedDocumentResponse:
    """Parse resume from URL and return structured data"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting resume parsing - Request ID: {request.state.request_id}, User ID: {parse_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        # Download and extract text
        file_path = DocumentExtractor.download_file(str(parse_request.url), parse_request.filename)
        if not file_path:
            raise HTTPException(status_code=400, detail="Failed to download file")
            
        text = DocumentExtractor.extract_text(file_path, parse_request.filename)
        if text is None:  # Changed from "if not text" to handle None specifically
            page_count = DocumentExtractor.get_page_count(file_path, parse_request.filename)
            if page_count and page_count > DocumentExtractor.MAX_PAGES:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Document exceeds maximum limit of {DocumentExtractor.MAX_PAGES} pages"
                )
            raise HTTPException(status_code=400, detail="Failed to extract text from document")
            
        # Process resume text through parser
        parser = ResumeParser(ml_manager)
        parsed_data = await parser.parse(
            text, 
            userId=parse_request.userId, 
            background_tasks=background_tasks,
            companyId=parse_request.companyId,
            fileId=parse_request.fileId
        )
        
        return ParsedDocumentResponse(
            status="success",
            data={
                "userId": parse_request.userId,
                "companyId": parse_request.companyId,
                "fileId": parse_request.fileId,
                "raw_text": text,
                "parsed_data": parsed_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/parse/job")
async def parse_job(
    request: Request,
    parse_request: DocumentParseRequest,
    background_tasks: BackgroundTasks
) -> ParsedDocumentResponse:
    """Parse job description from URL and return structured data"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting job parsing - Request ID: {request.state.request_id}, User ID: {parse_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        # Download and extract text
        file_path = DocumentExtractor.download_file(str(parse_request.url), parse_request.filename)
        if not file_path:
            raise HTTPException(status_code=400, detail="Failed to download file")
            
        text = DocumentExtractor.extract_text(file_path, parse_request.filename)
        if text is None:  # Changed from "if not text" to handle None specifically
            page_count = DocumentExtractor.get_page_count(file_path, parse_request.filename)
            if page_count and page_count > DocumentExtractor.MAX_PAGES:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Document exceeds maximum limit of {DocumentExtractor.MAX_PAGES} pages"
                )
            raise HTTPException(status_code=400, detail="Failed to extract text from document")
            
        # Process job text through parser
        parser = JobParser(ml_manager)
        parsed_data = await parser.parse(
            text, 
            userId=parse_request.userId, 
            companyId=parse_request.companyId or "", 
            background_tasks=background_tasks
        )
        
        return ParsedDocumentResponse(
            status="success",
            data={
                "userId": parse_request.userId,
                "companyId": parse_request.companyId,
                "raw_text": text,
                "parsed_data": parsed_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/match/resume-to-jobs")
async def match_resume_to_jobs(
    request: Request,
    match_request: ResumeToJobsRequest
) -> MatchingResponse:
    """Match a resume against multiple jobs"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting resume-to-jobs matching - Request ID: {request.state.request_id}, User ID: {match_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        matcher = ResumeToJobsMatcher()
        matches = await matcher.match(
            resume_data=match_request.resume,
            jobs=match_request.jobs
        )
        
        return MatchingResponse(
            status="success",
            data={
                "userId": match_request.userId,
                "matches": matches
            }
        )
    except Exception as e:
        logger.error(f"Error in resume-to-jobs matching: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/match/job-to-resumes")
async def match_job_to_resumes(
    request: Request,
    match_request: JobToResumesRequest
) -> MatchingResponse:
    """Match a job against multiple resumes"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting job-to-resumes matching - Request ID: {request.state.request_id}, User ID: {match_request.userId}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
        matcher = JobToResumesMatcher()
        matches = await matcher.match(
            jobId=match_request.jobId,
            job_data=match_request.job,
            resumes=match_request.resumes,
            options=match_request.options
        )
        
        return MatchingResponse(
            status="success",
            data={
                "userId": match_request.userId,
                "jobId": match_request.jobId,
                "matches": matches,
                "options": match_request.options
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/parse/contact")
async def parse_contact(
    request: Request,
    parse_request: ContactParseRequest,
    background_tasks: BackgroundTasks
) -> ParsedDocumentResponse:
    """Parse contact information from resume text and return structured data"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting contact parsing - Request ID: {request.state.request_id}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
            
        # Process job text through parser
        parser = ResumeContactInfoParser(ml_manager)
        parsed_data = await parser.parse(
            parse_request.resumeText
        )
        
        # Add cleanup to background tasks
        background_tasks.add_task(cleanup_resources)
        
        return ParsedDocumentResponse(
            status="success",
            data={
                "resumeId": parse_request.resumeId,
                "parsed_data": parsed_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # Ensure cleanup happens even if there's an error
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

@app.post("/parse/job-v2")
async def parse_job_v2(
    request: Request,
    parse_request: JobHeaderParseRequest,
    background_tasks: BackgroundTasks
) -> ParsedDocumentResponse:
    """Parse header information from job text and return structured data"""
    # Use the global logger from logging_helpers
    logger.info(f"Starting job header parsing - Request ID: {request.state.request_id}")
    
    global ml_manager
    if not ml_manager:
        logger.error("ML service not initialized")
        raise MLProcessingError("ML service not initialized")
        
    try:
            
        # Process job text through parser
        parser = JobParserV2(ml_manager)
        parsed_data = await parser.parse(
            parse_request.jobText
        )
        
        # Add cleanup to background tasks
        background_tasks.add_task(cleanup_resources)
        
        return ParsedDocumentResponse(
            status="success",
            data={
                "jobId": parse_request.jobId,
                "parsed_data": parsed_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # Ensure cleanup happens even if there's an error
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
@app.get("/health/startup")
async def startup_check():
    """Startup check endpoint for startup probe"""
    # Use the global logger from logging_helpers
    try:
        # Check if ML manager is initialized and model is loaded
        if not ml_manager or not ml_manager.model_singleton.model:
            logger.info("Startup check: Model still initializing")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "initializing",
                    "message": "Model still loading",
                    "initialization_state": "in_progress"
                }
            )
            
        return {
            "status": "ready",
            "message": "Initial model loading complete"
        }
    except Exception as e:
        logger.error(f"Startup check error: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "message": str(e)
            }
        )

@app.get("/health/liveness")
async def liveness_check():
    """Basic health check endpoint for liveness probe"""
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc)
    }

@app.get("/health/readiness")
async def readiness_check():
    """Readiness check endpoint for readiness probe"""
    # Use the global logger from logging_helpers
    try:
        # Check if ML manager is initialized
        if not ml_manager:
            logger.error("Readiness check failed: ML manager not initialized")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "message": "ML service not initialized"
                }
            )
        
        # Check if model is loaded
        if not ml_manager.model_singleton.model:
            logger.error("Readiness check failed: Model not loaded")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "message": "Model not loaded"
                }
            )
            
        # Check GPU status if available
        gpu_info = {}
        if torch.cuda.is_available():
            gpu_info = {
                "device": torch.cuda.get_device_name(0),
                "memory_allocated": f"{torch.cuda.memory_allocated()/1024**3:.2f}GB",
                "memory_reserved": f"{torch.cuda.memory_reserved()/1024**3:.2f}GB"
            }
            
        return {
            "status": "ready",
            "gpu_info": gpu_info,
            "model_loaded": True
        }
    except Exception as e:
        logger.error(f"Readiness check error: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "message": str(e)
            }
        )

# Keep the original /health endpoint for backward compatibility
@app.get("/health")
async def health_check():
    """Legacy health check endpoint"""
    return await readiness_check()



import uuid
import time
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from ..utils.logging_helpers import set_request_id, clear_request_id

class RequestTrackingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate request ID
        request_id = str(uuid.uuid4())

        # Set request ID and start time in request state
        request.state.request_id = request_id
        request.state.start_time = time.time()

        # Set request ID in thread-local storage for logging
        set_request_id(request_id)

        try:
            response = await call_next(request)
            return response
        finally:
            # Clear request ID from thread-local storage
            clear_request_id()

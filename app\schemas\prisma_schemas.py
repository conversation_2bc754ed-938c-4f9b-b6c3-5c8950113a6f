RESUME_SCHEMA = """
model Resume {
  standard_fields    StandardFields
  additional_sections AdditionalSection[]
}

model StandardFields {
  header     HeaderSection
  summary    SummarySection
  experience ExperienceSection[]
  education  EducationSection[]
  skills     String[]
}

model HeaderSection {
  name        String
  email    String
  phone    String[]
  website  String?
  twitter  String?
  linkedin String?
  github   String?
  address   String?
}
model SummarySection {
  content String?    // Summary of profile
}

model ExperienceSection {
  start_date   String      // YYYY-MM or YYYY
  end_date     String      // YYYY-MM or YYYY or "Present"
  title        String
  company      String
  employmentType      String // Full-time, Part-time, Contract, Internship, Freelance
  location     String?
  description  String[]    // Array of bullet points
  highlights   String[]?   // Optional key achievements or metrics
  technologies String[]?   // Optional specific technologies used
}

model EducationSection {
  start_date   String      // YYYY
  end_date     String      // YYYY
  degree       String
  institution  String
  location     String?
  details      String[]    // Array of additional details
  gpa          String?     // Optional GPA
  honors       String[]?   // Optional honors/awards
}

model AdditionalSection {
  header   String     // header title of section
  details  String[]   // Array of additional details of that section
  type     String?    // Optional type classifier (e.g., "certifications", "projects", "publications")
}
"""

RESUME_MATCH_SCHEMA = """
model MatchResult {
  resume_id String
  matches Match[]
  summary String?    // Optional overall summary of all matches
}

model Match {
  job_id           String
  match_scores     MatchScores
  match_analysis   String
  matching_skills  String[]
  missing_skills   String[]
  experience_match String     // "high" | "medium" | "low"
  recommendations  String[]
  key_strengths    String[]?  // Optional key strengths for this specific job
}

model MatchScores {
  overall_score         Int     // 0-100
  skills_match         Int     // 0-100
  experience_alignment Int     // 0-100
  education_fit        Int     // 0-100
  role_relevance      Int?    // Optional 0-100 for role-specific scoring
}
"""

RESUME_EVALUATION_SCHEMA = """
model UserResumeEvaluation {
  id             String   @id @default(uuid())
  userId         String?
  companyId      String?
  evaluationData String
  createdAt      DateTime @default(now())
}
"""
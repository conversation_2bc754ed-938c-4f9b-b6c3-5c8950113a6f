from pydantic import BaseModel, Field, HttpUrl
from typing import List, Dict, Any, Optional

class BaseRequest(BaseModel):
    """Base request schema with common fields"""
    userId: str = Field(..., description="User ID for request tracking")
    companyId: Optional[str] = Field(None, description="Company ID for request tracking")
    fileId: Optional[str] = Field(None, description="File ID for request tracking")

class ResumeEvalRequest(BaseRequest):
    """Schema for resume evaluation requests"""
    resumeId: str = Field(..., description="Resume ID for request tracking")
    resume_data: Dict[str, Any] = Field(
        ...,
        description="Resume data including ID and raw text"
    )
    options: Optional[Dict[str, Any]] = Field(
        default={},
        description="Optional evaluation parameters"
    )

class ResumeEvalPlainRequest(BaseRequest):
    """Schema for resume evaluation requests with plain text input"""
    resumeId: str = Field(..., description="Resume ID for request tracking")
    resume_text: str = Field(...,
        description="Plain text content of the resume"
    )

class DocumentParseRequest(BaseRequest):
    """Schema for document parsing requests"""
    url: HttpUrl = Field(..., description="URL of the document to parse")
    filename: str = Field(..., description="Original filename with extension (e.g. 'resume.pdf')")

class ResumeToJobsRequest(BaseRequest):
    """Schema for matching a resume to multiple jobs"""
    resume: Dict[str, Any] = Field(
        ...,
        description="Resume data including ID and raw text"
    )
    jobs: List[Dict[str, Any]] = Field(
        ...,
        description="List of jobs to match against"
    )
    options: Optional[Dict[str, Any]] = Field(
        default={},
        description="Optional matching parameters"
    )

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "resume": {
                        "id": "1",
                        "raw_text": "Full resume text here"
                    },
                    "jobs": [
                        {
                            "id": "2",
                            "raw_text": "First job description text"
                        },
                        {
                            "id": "3",
                            "raw_text": "Second job description text"
                        }
                    ],
                    "options": {
                        "consider_experience_level": True,
                        "skills_weight": 0.6,
                        "experience_weight": 0.4
                    }
                }
            ]
        }
    }

class JobToResumesRequest(BaseRequest):
    """Schema for matching a job to multiple resumes"""    
    jobId: str = Field(..., description="Job ID for request tracking")
    job: Dict[str, Any] = Field(
        ...,
        description="Job data including ID and raw text"
    )
    resumes: List[Dict[str, Any]] = Field(
        ...,
        description="List of resumes to match against"
    )
    options: Optional[Dict[str, Any]] = Field(
        default={},
        description="Optional matching parameters"
    )

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "job": {"id": "1", "raw_text": "Full job description text here"},
                    "resumes": [
                        {"id": "2", "raw_text": "Resume text here"},
                        {"id": "3", "raw_text": "Another resume text here"}
                    ]
                }
            ]
        }
    }

class ContactParseRequest(BaseRequest):
    """Schema for contact parsing requests"""
    resumeId: str = Field(..., description="Resume ID for request tracking")
    resumeText: str = Field(..., description="Resume text to parse for contact information")
    
class JobHeaderParseRequest(BaseRequest):
    """Schema for contact parsing requests"""
    jobId: str = Field(..., description="Job ID for request tracking")
    jobText: str = Field(..., description="JOb text to parse for job header information")
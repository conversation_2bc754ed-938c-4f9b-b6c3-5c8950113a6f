from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class ParsedDocumentResponse(BaseModel):
    """Schema for document parsing response"""
    status: str = Field(..., pattern="^(success|error)$")
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class MatchingResponse(BaseModel):
    """Schema for matching response"""
    status: str = Field(..., pattern="^(success|error)$")
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class ProcessingResponse(BaseModel):
    """
    Standardized response format for all processing endpoints
    
    Attributes:
        request_id: Unique identifier for the request
        status: Processing status (success/error)
        data: Processed data (if successful)
        error: Error message (if failed)
        processing_time: Processing time in seconds
    """
    request_id: str
    status: str = Field(..., pattern="^(success|error)$")
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: float = Field(
        ...,
        description="Processing time in seconds",
        ge=0.0
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="UTC timestamp of the response"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

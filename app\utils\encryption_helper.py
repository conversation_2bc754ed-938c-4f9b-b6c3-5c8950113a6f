import os
import hashlib
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.backends import default_backend

# Configuration constants (must match TypeScript)
SALT_LENGTH = 16
IV_LENGTH = 12
AUTH_TAG_LENGTH = 16
KEY_LENGTH = 32  # For AES-256

# scrypt parameters (must match Node.js defaults used by crypto.scryptSync if not specified)
SCRYPT_N = 16384  # Cost factor
SCRYPT_R = 8      # Block size
SCRYPT_P = 1      # Parallelization factor

# Retrieve the encryption password from environment variable
ENCRYPTION_PASSWORD_STR = os.getenv('ENCRYPTION_PASSWORD')
if not ENCRYPTION_PASSWORD_STR:
    raise ValueError("ENCRYPTION_PASSWORD environment variable not set.")
ENCRYPTION_PASSWORD_BYTES = ENCRYPTION_PASSWORD_STR.encode('utf-8')

def encrypt_to_buffer(text: str) -> bytes:
    """
    Encrypts a string and returns bytes containing:
    [salt | iv | ciphertext | authTag]
    """
    salt = os.urandom(SALT_LENGTH)
    iv = os.urandom(IV_LENGTH)

    # Derive key using scrypt
    key = hashlib.scrypt(
        ENCRYPTION_PASSWORD_BYTES,
        salt=salt,
        n=SCRYPT_N,
        r=SCRYPT_R,
        p=SCRYPT_P,
        dklen=KEY_LENGTH
    )

    aesgcm = AESGCM(key)
    plaintext_bytes = text.encode('utf-8')
    
    # encrypt() returns ciphertext + tag
    ciphertext_and_tag = aesgcm.encrypt(iv, plaintext_bytes, None) # No associated data

    # The tag is the last AUTH_TAG_LENGTH bytes of the result
    # The ciphertext is everything before the tag
    # This step is implicitly handled if we store ciphertext_and_tag directly,
    # but to match the Node.js structure where ciphertext and authTag are separate before concat:
    ciphertext = ciphertext_and_tag[:-AUTH_TAG_LENGTH]
    auth_tag = ciphertext_and_tag[-AUTH_TAG_LENGTH:]

    if len(auth_tag) != AUTH_TAG_LENGTH:
        # This should not happen with AESGCM default tag size if AUTH_TAG_LENGTH is 16
        raise ValueError("Auth tag length mismatch.")

    return salt + iv + ciphertext + auth_tag

def decrypt_from_buffer(encrypted_data: bytes) -> str:
    """
    Decrypts bytes containing: [salt | iv | ciphertext | authTag]
    """
    min_length = SALT_LENGTH + IV_LENGTH + AUTH_TAG_LENGTH
    if not isinstance(encrypted_data, bytes) or len(encrypted_data) < min_length:
        # Handle error appropriately, e.g., raise ValueError or return None/empty string
        # Matching TypeScript's behavior of returning an empty string on basic validation failure
        print("Decryption failed: Encrypted data is too short or not bytes.")
        return ""

    try:
        salt = encrypted_data[:SALT_LENGTH]
        iv = encrypted_data[SALT_LENGTH : SALT_LENGTH + IV_LENGTH]
        # Ciphertext and tag are combined in the remaining part
        ciphertext_with_auth_tag = encrypted_data[SALT_LENGTH + IV_LENGTH :]
        
        # The actual ciphertext is all but the last AUTH_TAG_LENGTH bytes of this remainder
        # The auth_tag is the last AUTH_TAG_LENGTH bytes
        # However, AESGCM.decrypt expects the combined ciphertext + tag
        
        # Re-derive key using scrypt
        key = hashlib.scrypt(
            ENCRYPTION_PASSWORD_BYTES,
            salt=salt,
            n=SCRYPT_N,
            r=SCRYPT_R,
            p=SCRYPT_P,
            dklen=KEY_LENGTH
        )

        aesgcm = AESGCM(key)
        
        # Decrypt (expects IV and ciphertext_with_auth_tag)
        decrypted_bytes = aesgcm.decrypt(iv, ciphertext_with_auth_tag, None) # No associated data
        return decrypted_bytes.decode('utf-8')
        
    except Exception as e: # Catching a broad exception, cryptography.exceptions.InvalidTag is specific for tag mismatch
        print(f"Decryption failed: {e}")
        return "" # Match TypeScript's behavior of returning an empty string on error

# Example Usage:
if __name__ == "__main__":
    if not ENCRYPTION_PASSWORD_STR:
        print("Skipping example: ENCRYPTION_PASSWORD not set.")
    else:
        original_text = "This is a secret message!"
        print(f"Original: {original_text}")

        encrypted_blob = encrypt_to_buffer(original_text)
        print(f"Encrypted (bytes): {encrypted_blob}")
        # For display or storage, you might Base64 encode it:
        import base64
        encrypted_base64 = base64.b64encode(encrypted_blob).decode('utf-8')
        print(f"Encrypted (Base64): {encrypted_base64}")

        # To decrypt_from_buffer, you'd first Base64 decode if it was stored that way
        # decrypted_blob_from_base64 = base64.b64decode(encrypted_base64.encode('utf-8'))
        # decrypted_text = decrypt_from_buffer(decrypted_blob_from_base64)
        
        decrypted_text = decrypt_from_buffer(encrypted_blob)
        print(f"Decrypted: {decrypted_text}")

        if original_text == decrypted_text:
            print("Encryption and decryption successful!")
        else:
            print("Error: Decrypted text does not match original.")

        # --- Test with data from Node.js (if you have an example) ---
        # Example: if you have a base64 string from Node.js encryption
        # node_encrypted_base64 = "your_base64_encrypted_string_from_nodejs"
        # try:
        #     node_encrypted_bytes = base64.b64decode(node_encrypted_base64.encode('utf-8'))
        #     decrypted_from_node = decrypt_from_buffer(node_encrypted_bytes)
        #     print(f"Decrypted from Node.js: {decrypted_from_node}")
        # except Exception as e:
        #     print(f"Failed to decrypt data from Node.js: {e}")

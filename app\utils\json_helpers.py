import re
import json
from typing import Any, Dict
from datetime import datetime
from .logging_helpers import get_logger

logger = get_logger(__name__)

def extract_and_sanitize_json_from_llm_output(raw_output: str) -> Dict[str, Any]:
    try:
        # Remove prompt and example junk
        model_output_only = raw_output.split('[/INST]')[-1].strip()
        logger.debug(f"Model output after splitting: {model_output_only[:200]}...")  # First 200 chars

        # Extract the real JSON object the model returned
        json_text = extract_json_block(model_output_only)
        logger.info(f"Extracted JSON block length: {len(json_text)}")
        logger.debug(f"JSON block start: {json_text[:200]}...")  # First 200 chars

        # Clean it
        json_text = sanitize_json_string(json_text)
        logger.debug(f"Sanitized JSON length: {len(json_text)}")
        
        # Escape it
        json_text = escape_unescaped_newlines(json_text)
        logger.debug(f"Escaped JSON length: {len(json_text)}")

        # Parse it
        parsed_json = json.loads(json_text)
        logger.info("JSON successfully parsed")
        return parsed_json

    except Exception as e:
        logger.error(f"JSON extraction/sanitization failed: {e}")
        logger.error(f"Raw output length: {len(raw_output)}")
        logger.error(f"Raw output preview: {raw_output[:500]}...")  # First 500 chars
        raise
    
def extract_json_block(text: str) -> str:
    """
    Extract the first valid top-level JSON object from a string using a character-level parser.
    """
    depth = 0
    start = None

    for i, ch in enumerate(text):
        if ch == '{':
            if depth == 0:
                start = i
            depth += 1
        elif ch == '}':
            depth -= 1
            if depth == 0 and start is not None:
                return text[start:i + 1]

    raise ValueError("No complete JSON object found in response.")

def sanitize_json_string(s: str) -> str:
    import unicodedata
    s = unicodedata.normalize("NFKC", s)

    # Replace smart quotes, dashes, etc.
    s = s.replace("“", '"').replace("”", '"').replace("‘", "'").replace("’", "'")
    s = s.replace("–", "-").replace("—", "-").replace("…", "...")

    # Remove zero-width and invisible characters
    s = re.sub(r'[\u200B-\u200D\uFEFF\u202A-\u202E]', '', s)

    # Remove control characters (except \n \t \r)
    s = ''.join(c for c in s if ord(c) >= 32 or c in '\n\r\t')

    # Strip malformed escape sequences
    s = re.sub(r'\\u00[0-1][0-9a-fA-F]', '', s)
    s = re.sub(r'\\x0[0-9a-fA-F]', '', s)

    return s.strip()

def escape_unescaped_newlines(json_str: str) -> str:
    """
    Replace any raw newlines that appear inside string values with literal \\n,
    which is required by JSON spec.
    """
    def fix_linebreaks_in_strings(match):
        content = match.group(0)
        return content.replace('\n', '\\n')

    # Regex to find JSON string values
    string_regex = r'"(?:[^"\\]|\\.)*?"'

    return re.sub(string_regex, fix_linebreaks_in_strings, json_str)


def format_timestamp(timestamp: Any = None) -> str:
    """
    Format timestamp to ISO format string, handling various input types
    
    Args:
        timestamp: Input timestamp (datetime, str, or None)
    
    Returns:
        ISO formatted timestamp string
    """
    try:
        if timestamp is None:
            return datetime.utcnow().isoformat()
        
        if isinstance(timestamp, datetime):
            return timestamp.isoformat()
        
        if isinstance(timestamp, str):
            # If it's already a properly formatted string, return it
            if 'T' in timestamp or 'Z' in timestamp:
                return timestamp
            try:
                return datetime.fromisoformat(timestamp).isoformat()
            except ValueError:
                return datetime.utcnow().isoformat()
                
        return datetime.utcnow().isoformat()
    except Exception as e:
        logger.error(f"Error formatting timestamp: {e}")
        return datetime.utcnow().isoformat()

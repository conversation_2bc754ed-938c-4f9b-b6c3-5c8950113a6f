import logging
import threading

_thread_local = threading.local()

class RequestIdAdapter(logging.LoggerAdapter):
    """
    A logger adapter to inject a request_id from thread-local storage into the log.
    """
    def process(self, msg, kwargs):
        # The default 'NO_REQ_ID' is used if set_request_id has not been called for the current thread.
        request_id = getattr(_thread_local, 'request_id', 'NO_REQ_ID')
        # Add request_id to the 'extra' dict, which is used by the formatter.
        kwargs['extra'] = kwargs.get('extra', {})
        kwargs['extra']['request_id'] = request_id
        return msg, kwargs

def get_logger(name: str) -> RequestIdAdapter:
    """
    Get a logger instance wrapped with the RequestIdAdapter.
    """
    logger = logging.getLogger(name)
    return RequestIdAdapter(logger, {})

def set_request_id(request_id: str):
    """
    Sets the request_id for the current thread.
    This should be called at the beginning of a request.
    """
    _thread_local.request_id = request_id

def clear_request_id():
    """
    Clears the request_id from the current thread.
    This should be called at the end of a request.
    """
    if hasattr(_thread_local, 'request_id'):
        del _thread_local.request_id
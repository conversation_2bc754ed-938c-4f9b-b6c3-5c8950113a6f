import re
import random
from typing import Union, List, Optional

def safe_field(field: Union[str, List[str], None]) -> str:
    if field is None:
        return ''
    if isinstance(field, list):
        return ', '.join(str(item).strip() for item in field if item is not None)
    return str(field).strip()

def normalize_headers(header: Optional[str]) -> str:
    if not header:
        return ''
    
    header = str(header).strip() # Ensure it's a string and stripped
    
    # Handle special cases for common name prefixes
    prefixes = ['mc', 'mac', 'o\'', 'van', 'von', 'de', 'la', 'du', 'di', 'del']
    
    words = header.split(' ')
    normalized_words = []
    
    for word in words:
        lower_word = word.lower()
        processed_word = None
        
        # Check for prefixes
        for prefix in prefixes:
            if lower_word.startswith(prefix) and len(lower_word) > len(prefix):
                # Capitalize the letter after the prefix
                processed_word = (prefix[0].upper() + 
                                  prefix[1:] + 
                                  lower_word[len(prefix)].upper() + 
                                  lower_word[len(prefix)+1:])
                break
        
        if processed_word:
            normalized_words.append(processed_word)
            continue
            
        # Handle hyphenated names (like <PERSON><PERSON>)
        if '-' in lower_word:
            normalized_words.append('-'.join(
                part[0].upper() + part[1:] for part in lower_word.split('-') if part
            ))
            continue
        
        # Standard case: capitalize first letter only
        if lower_word: # Ensure word is not empty after lowercasing
            normalized_words.append(lower_word[0].upper() + lower_word[1:])
        else:
            normalized_words.append('') # Append empty string if word was empty
            
    return ' '.join(normalized_words)

def generate_username(name: str) -> str:
    clean_name = re.sub(r'\s+', '', name).lower() # remove spaces and lowercase
    random_digits = random.randint(10000000, 99999999) # 8-digit number
    return f"{random_digits}-{clean_name}"


import re
from .logging_helpers import get_logger

logger = get_logger(__name__)

def clean_text(text: str) -> str:
    """Clean extracted text by removing special characters and normalizing formatting.
    
    Args:
        text (str): Raw text to clean
        
    Returns:
        str: Cleaned text
    """
    if not text:
        return ""

    logger.info(f"Starting text cleaning. Initial length: {len(text)}")

    # First remove control characters
    text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
    logger.info(f"After control character removal. Length: {len(text)}")

    # Basic cleanup
    text = re.sub(r'\s+', ' ', text)  # Normalize whitespace within lines
    text = text.replace('•', '')  # Remove bullet points
    
    # Split into lines and clean
    lines = []
    for line in text.split('\n'):
        line = line.strip()
        if line:
            # Remove any remaining special characters
            line = re.sub(r'[^\w\s:,.\-@()/]', '', line)
            lines.append(line)
            
    # Join lines back together
    cleaned_text = '\n'.join(lines)
    
    # Add double newlines between sections for better readability
    cleaned_text = re.sub(r'\n(?=[A-Z][a-z])', '\n\n', cleaned_text)
    
    cleaned_text = cleaned_text.strip()
    logger.info(f"Finished text cleaning. Final length: {len(cleaned_text)}")
    
    return cleaned_text

def lightly_clean_text(raw: str) -> str:
    import re

    # Remove extra spaces around punctuation
    cleaned = re.sub(r'\s{2,}', ' ', raw)
    cleaned = re.sub(r'•', '-', cleaned)
    cleaned = re.sub(r'\n{2,}', '\n', cleaned)
    
    return cleaned.strip()
from docx import Document
import pdfplumber
import requests
import tempfile
import os
from typing import Optional, Tuple
from app.utils.text_cleaner import clean_text
from app.utils.logging_helpers import get_logger
logger = get_logger(__name__)

class DocumentExtractor:
    SUPPORTED_EXTENSIONS = {'.pdf', '.docx'}
    MAX_PAGES = 8  # Maximum allowed pages
    
    @staticmethod
    def download_file(url: str, filename: str) -> Optional[str]:
        """
        Download file from URL to temp location
        
        Args:
            url: The storage service URL
            filename: Original filename with extension (e.g. 'resume.pdf')
        """
        try:
            # Get extension from filename
            ext = os.path.splitext(filename)[1].lower()
            if ext not in DocumentExtractor.SUPPORTED_EXTENSIONS:
                logger.error(f"Unsupported file type: {ext}")
                return None

            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # Save to temp file with correct extension
            with tempfile.NamedTemporaryFile(delete=False, suffix=ext) as tmp_file:
                for chunk in response.iter_content(chunk_size=8192):
                    tmp_file.write(chunk)
                return tmp_file.name
                
        except Exception as e:
            logger.error(f"Error downloading file: {str(e)}")
            return None
        
    @staticmethod    
    def extract_text_from_pdf(file_path):
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text

    @staticmethod
    def get_page_count(file_path: str, filename: str) -> Optional[int]:
        """Get the number of pages in a document"""
        try:
            ext = os.path.splitext(filename)[1].lower()
            
            if ext == '.pdf':
                with pdfplumber.open(file_path) as pdf:
                    return len(pdf.pages)
                    
            elif ext == '.docx':
                doc = Document(file_path)
                # Approximate page count for Word documents
                # Assuming ~400 words per page (industry standard)
                total_words = sum(len(paragraph.text.split()) for paragraph in doc.paragraphs)
                return (total_words + 399) // 400  # Round up division
                
            return None
            
        except Exception as e:
            logger.error(f"Error counting pages: {str(e)}")
            return None
        
    @staticmethod
    def extract_text(file_path: str, filename: str) -> Optional[str]:
        """Extract text from PDF or DOCX file"""
        try:
            # Check page count first
            page_count = DocumentExtractor.get_page_count(file_path, filename)
            
            if page_count is None:
                logger.error("Could not determine page count")
                return None
                
            if page_count > DocumentExtractor.MAX_PAGES:
                logger.error(f"Document exceeds maximum page limit of {DocumentExtractor.MAX_PAGES} pages")
                return None
            
            # Get extension from filename instead of file_path
            ext = os.path.splitext(filename)[1].lower()
            
            if ext == '.pdf':
                logger.info("Processing PDF file with pdfplumber")
                text = ""
                with pdfplumber.open(file_path) as pdf:
                    for i, page in enumerate(pdf.pages):
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                            logger.info(f"Page {i+1}: Extracted {len(page_text)} characters")
                        else:
                            logger.warning(f"Page {i+1}: No text extracted")
                if not text.strip():
                    raise ValueError("No text content extracted from PDF")
                logger.info(f"Successfully extracted {len(text)} characters from PDF")
                return clean_text(text)
                    
            elif ext == '.docx' or ext == '.doc':
                logger.info("Processing DOCX file")
                doc = Document(file_path)
                text = '\n'.join([para.text for para in doc.paragraphs])
                if not text.strip():
                    logger.warning("No text content extracted from DOCX")
                    raise ValueError("No text content extracted from DOCX")
                logger.info(f"Successfully extracted {len(text)} characters from DOCX")
                # Log a preview of the extracted text
                preview = text[:200] + "..." if len(text) > 200 else text
                logger.info(f"Extracted text preview: {preview}")
                
                return clean_text(text)
                
            else:
                raise ValueError(f"Unsupported file type: {ext}")
                
        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            return None
        finally:
            # Cleanup temp file
            try:
                os.unlink(file_path)
            except:
                pass

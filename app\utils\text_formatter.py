import re

def structure_resume_text(raw_text: str) -> str:
    """
    Converts raw resume text into a prompt-ready format with flexible section mapping.
    Unknown or non-standard headers go into ADDITIONAL sections.
    """
    lines = raw_text.splitlines()
    output = []
    current_section = None

    section_mappings = {
        'CONTACT': ['contact', 'address', 'email', 'phone', 'details', 'links', 'place of birth', 'driving license'],
        'SUMMARY': ['summary', 'profile', 'objective', 'about me', 'bio'],
        'EXPERIENCE': ['experience', 'employment history', 'work history', 'career history', 'professional experience'],
        'EDUCATION': ['education', 'academic background', 'qualifications'],
        'SKILLS': ['skills', 'technical skills', 'competencies'],
        'COURSES': ['courses', 'trainings', 'certifications'],
        'ACCOMPLISHMENTS': ['accomplishments', 'achievements', 'awards'],
        'HOBBIES': ['hobbies', 'interests'],
        'LANGUAGES': ['languages', 'language proficiency']
    }

    def match_section(line):
        lowered = line.strip().lower()
        for anchor, variants in section_mappings.items():
            if any(keyword in lowered for keyword in variants):
                return anchor
        return None

    for line in lines:
        clean = line.strip()
        if not clean:
            continue

        detected = match_section(clean)
        is_likely_header = clean.isupper() and len(clean.split()) < 6

        if detected:
            if detected != current_section:
                current_section = detected
                output.append(f"[{detected}]")
            continue
        elif is_likely_header:
            current_section = f"ADDITIONAL: {clean.title()}"
            output.append(f"[{current_section}]")
            continue

        output.append(clean)

    formatted_text = '\n'.join(output)

    # Normalize double-date range ("Jan 2020  Jul 2021" → "Jan 2020 - Jul 2021")
    formatted_text = re.sub(r'(\w+ \d{4})\s{2,}(\w+ \d{4})', r'\1 - \2', formatted_text)

    # Normalize dates to YYYY-MM
    month_map = {
        'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
        'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
    }
    def normalize_date(match):
        month, year = match.group(1), match.group(2)
        return f"{year}-{month_map.get(month[:3].lower(), '01')}"

    formatted_text = re.sub(r'(?i)(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* (\d{4})', normalize_date, formatted_text)

    return f"<<<\n{formatted_text}\n>>>"
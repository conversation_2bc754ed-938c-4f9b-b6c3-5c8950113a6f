name: job-matching-ml-processing-service
location: westus3
properties:
  managedEnvironmentId: /subscriptions/${SUBSCRIPTION_ID}/resourceGroups/${RESOURCE_GROUP}/providers/Microsoft.App/managedEnvironments/${ENVIRONMENT_NAME}
  configuration:
    ingress:
      external: true
      targetPort: 8000
    secrets:
      - name: hf-token
        value: ${HF_TOKEN}
  template:
    containers:
      - name: ml-processing-service
        image: ${REGISTRY_NAME}.azurecr.io/ml-processing-service:latest
        env:
          - name: HF_TOKEN
            secretRef: hf-token
          - name: AI_MODEL
            value: ${AI_MODEL}
          - name: MAX_LENGTH
            value: "4096"
          - name: MAX_NEW_TOKENS
            value: "4096"
          - name: TEMPERATURE
            value: "0.0"
          - name: DO_SAMPLE
            value: "false"
          # Add GPU-related environment variables
          - name: NVIDIA_VISIBLE_DEVICES
            value: "all"
          - name: GPU_COUNT
            value: "1"
          - name: GPU_MEMORY_GB
            value: "8"
        resources:
          cpu: 4
          memory: 16Gi
          # Add GPU resource request
          gpu:
            count: 1
            sku: "Consumption-GPU-NC8as-T4"  # or "V100" depending on availability and needs
        probes:
          - type: liveness
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          - type: readiness
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10

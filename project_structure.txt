ml_service/
├── app/
│   ├── main.py                                 # Application entry point with FastAPI endpoints
│   ├── config/
│   │   └── settings.py                         # Configuration management with Settings class
│   ├── core/
│   │   ├── llm/
│   │   │   ├── processor/
│   │   │   │   └── processor.py                # LLM processing logic
│   │   │   ├── parser/
│   │   │   │   ├── resume_parser.py            # Resume parsing logic
│   │   │   │   └── job_parser.py               # Job parsing logic
│   │   │   ├── evaluator/
│   │   │   │   └── evaluate_resume.py          # Resume evaluation logic
│   │   │   ├── matcher/
│   │   │   │   ├── resume_to_jobs.py           # Resume to jobs matching
│   │   │   │   └── job_to_resumes.py           # Job to resumes matching
│   │   │   └── prompts/
│   │   │       ├── resume_parser_prompt.py     # Prompt used in resume parsing
│   │   │       ├── job_parser_prompt.py        # Prompt used in job parsing
│   │   │       ├── resume_evaluator_prompt.py  # Prompt used in resume evaluation
│   │   │       ├── resume_to_jobs_prompt.py    # Prompt used in resume to jobs matching
│   │   │       └── job_to_resumes_prompt.py    # Prompt used in job to resumes matching
│   │   └── manager/
│   │       ├── gpu_manager.py                  # GPU management
│   │       ├── ml_manager.py                   # ML operations management
│   │       └── model_singleton.py              # Model singleton pattern
│   ├── logging/
│   │   ├── logging.py                          # Logging configuration
│   │   └── exceptions.py                       # Custom exceptions
│   ├── middleware/
│   │   └── tracking.py                         # Request tracking middleware
│   ├── schemas/
│   │   ├── request.py                          # Input request models
│   │   └── response.py                         # Output response models
│   ├── utils/
│   │   ├── json_helpers.py                     # JSON processing utilities
│   │   ├── text_helpers.py                     # Text processing utilities
│   │   └── text_extractor.py                   # Document text extraction
├── debug/                                      # Debug outputs and temporary files
├── logs/                                       # Application log files
├── tests/                                      
│   ├── unit/                                   # Unit tests
│   │   ├── test_processor.py                   # Unit tests for processor
│   │   ├── test_parsers.py                     # Unit tests for parsers
│   │   └── test_matchers.py                    # Unit tests for matchers
│   ├── integration/                            
│   │   ├── test_endpoints.py                   # Integration tests for endpoints
│   │   └── test_flows.py                       # Integration tests for flows
│   └── other/                          
│       ├── test_endpoints_with_data.py         # Tests endpoints with data
│       ├── test_model_init.py                  # Test model initializations
│       └── test.http                           # Test HTTP requests
├── .env                                       # Environment variables
├── requirements.txt                           # Python dependencies
├── run.py                                     # Script to run the application
└── README.md                                  # Project documentation
# --- Web Framework ---
fastapi>=0.115.12   # Modern, fast (ASGI) web framework
uvicorn[standard]>=0.34.2 # ASGI server implementation for FastAPI

# --- Environment Configuration ---
python-dotenv       # Loads environment variables from a .env file
pydantic>=2.0.0     # Data validation and settings management using Python type hints
pydantic-settings   # Pydantic integration for managing application settings
typing-extensions>=4.8.0 # Backports and extensions for the typing module

# --- Machine Learning & LLM ---
torch               # PyTorch deep learning framework
torchvision         # PyTorch library for computer vision tasks
torchaudio          # PyTorch library for audio processing tasks
protobuf            # Protocol buffers, used for data serialization (dependency for some libraries)
transformers         # Hugging Face library for pre-trained models (LLMs, etc.)
accelerate           # Hugging Face library for easy distributed training/inference
bitsandbytes         # Library for 8-bit and 4-bit quantization, often used with LLMs
sentencepiece        # Required for some tokenizer models (e.g., many LLMs)

# --- Document Processing ---
python-docx>=1.1.2   # For parsing Word documents
PyPDF2>=3.0.1        # For basic PDF file operations (reading, splitting, merging)
requests>=2.31.0     # For downloading documents
pdfplumber>=0.11.6   # For extracting text from PDFs

# --- GPU Monitoring ---
nvidia-ml-py>=11.495.46  # For GPU monitoring (NVML bindings)

# --- HTTP Client & Testing ---
httpx>=0.28.1        # Asynchronous HTTP client, often used with FastAPI
pytest>=7.0.0        # Popular testing framework

# --- Logging & Async ---
python-json-logger  # Formatter for structured JSON logging

# --- Database ---
asyncpg             # Asynchronous PostgreSQL database driver

# --- Cryptography  ---
cryptography>=3.4.7  # Low-level cryptographic recipes and primitives
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_parse_resume():
    # Test resume parsing endpoint
    pass

def test_parse_job():
    # Test job parsing endpoint
    pass

def test_match_resume_to_jobs():
    # Test resume to jobs matching endpoint
    pass

def test_match_job_to_resumes():
    # Test job to resumes matching endpoint
    pass
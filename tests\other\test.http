### Test Resume Evaluation
POST http://localhost:8000/evaluate/resume
Content-Type: application/json

{
    "resume_data": {
        "standard_fields": {
            "name": "<PERSON>",
            "email": "",
            "phone": ""
        },
        "experience": [
            {
                "start_date": "2018",
                "end_date": "Present",
                "title": "Senior Software Engineer",
                "location": "Tech Corp",
                "description": "- Led development of cloud-native applications using Python and AWS\n- Managed team of 5 developers\n- Implemented CI/CD pipelines"
            },
            {
                "start_date": "2015",
                "end_date": "2018",
                "title": "Software Engineer",
                "location": "Startup Inc",
                "description": "- Full-stack development with React and Node.js\n- Improved application performance by 40%"
            }
        ],
        "education": [
            {
                "start_date": "2013",
                "end_date": "2015",
                "title": "M.S. Computer Science",
                "location": "Stanford University",
                "description": ""
            },
            {
                "start_date": "2009",
                "end_date": "2013",
                "title": "B.S. Computer Science",
                "location": "UC Berkeley",
                "description": ""
            }
        ],
        "skills": [
            "Python",
            "JavaScript",
            "Java",
            "React",
            "Node.js",
            "Django",
            "AWS",
            "Docker",
            "Kubernetes",
            "CI/CD",
            "Agile",
            "Team Leadership"
        ],
        "additional_sections": {},
        "id": "resume123"
    }
}

### Test Resume to Jobs Matching
POST http://localhost:8000/match/resume-to-jobs
Content-Type: application/json

{
    "resume": {
        "id": "resume123",
        "raw_text": "John Doe\nSenior Software Engineer\n\nEXPERIENCE\nTech Corp (2018-Present)\n- Led development of cloud-native applications using Python and AWS\n- Managed team of 5 developers\n\nSKILLS\nPython, AWS, Leadership"
    },
    "jobs": [
        {
            "id": "job1",
            "raw_text": "Senior Python Developer\n\nWe're seeking a Senior Python Developer with strong AWS experience and team leadership skills. Must have experience with cloud-native applications and microservices architecture.\n\nRequirements:\n- Python\n- AWS\n- Leadership\n- Microservices"
        },
        {
            "id": "job2",
            "raw_text": "Frontend Developer\n\nLooking for a Frontend Developer with React.js experience. Knowledge of UI/UX principles is required.\n\nRequirements:\n- React.js\n- JavaScript\n- UI/UX\n- HTML/CSS"
        }
    ],
    "options": {
        "consider_experience_level": true,
        "skills_weight": 0.6,
        "experience_weight": 0.4
    }
}

### Test Job to Resumes Matching
POST http://localhost:8000/match/job-to-resumes
Content-Type: application/json

{
    "job": {
        "id": "job123",
        "raw_text": "Senior Software Engineer\n\nRequirements:\n- 5+ years experience in Python\n- Strong background in web development\n- Experience with React.js"
    },
    "resumes": [
        {
            "id": "resume1",
            "raw_text": "EXPERIENCE\n- 7 years Python development\n- Full stack web development\n\nSKILLS\nPython, Django, React.js, JavaScript"
        },
        {
            "id": "resume2",
            "raw_text": "EXPERIENCE\n- 3 years React.js development\n- UI/UX design\n\nSKILLS\nReact.js, JavaScript, HTML, CSS"
        }
    ],
    "options": {
        "consider_experience_level": true,
        "skills_weight": 0.6,
        "experience_weight": 0.4
    }
}






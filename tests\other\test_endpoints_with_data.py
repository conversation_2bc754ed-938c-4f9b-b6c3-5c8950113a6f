import requests
import json
from typing import Dict, Any

def test_endpoint(endpoint: str, data: Dict[str, Any]) -> None:
    """Test an endpoint and print the response"""
    url = f"http://localhost:8000/{endpoint}"
    try:
        response = requests.post(url, json=data)
        print(f"\nTesting {endpoint}:")
        print(f"Status Code: {response.status_code}")
        print("Response:", json.dumps(response.json(), indent=2))
    except Exception as e:
        print(f"Error testing {endpoint}:", str(e))

# Test data for resume to jobs matching
resume_to_jobs_data = {
    "resume": {
        "id": "resume123",
        "raw_text": """
        <PERSON>
        Senior Software Engineer

        EXPERIENCE
        Tech Corp (2018-Present)
        - Led development of cloud-native applications using Python and AWS
        - Managed team of 5 developers

        SKILLS
        Python, AWS, Leadership
        """
    },
    "jobs": [
        {
            "id": "job1",
            "raw_text": """
            Senior Python Developer

            We're seeking a Senior Python Developer with strong AWS experience and team leadership skills.
            Must have experience with cloud-native applications and microservices architecture.

            Requirements:
            - Python
            - AWS
            - Leadership
            - Microservices
            """
        },
        {
            "id": "job2",
            "raw_text": """
            Frontend Developer

            Looking for a Frontend Developer with React.js experience.
            Knowledge of UI/UX principles is required.

            Requirements:
            - React.js
            - JavaScript
            - UI/UX
            - HTML/CSS
            """
        }
    ],
    "options": {
        "consider_experience_level": True,
        "skills_weight": 0.6,
        "experience_weight": 0.4
    }
}

if __name__ == "__main__":
    # Test resume to jobs matching
    test_endpoint("match/resume-to-jobs", resume_to_jobs_data)
    # Add other test calls here



import torch
from transformers.models.auto.tokenization_auto import AutoTokenizer
from transformers.models.auto.modeling_auto import AutoModelForCausalLM
from transformers.utils.quantization_config import BitsAndBytesConfig
import logging

logging.basicConfig(level=logging.INFO)

def test_model_init():
    model_name = "mistralai/Mistral-7B-Instruct-v0.3"  # Use the same as in your settings
    
    try:
        # Test tokenizer first
        logging.info("Testing tokenizer initialization...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        logging.info("Tokenizer initialized successfully")
        
        # Test model initialization
        logging.info("Testing model initialization...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            quantization_config=BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16
            ),
            low_cpu_mem_usage=True
        )
        logging.info("Model initialized successfully")
        
        return True
        
    except Exception as e:
        logging.error(f"Initialization failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_model_init()
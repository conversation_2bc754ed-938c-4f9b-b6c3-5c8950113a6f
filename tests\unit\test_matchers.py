import pytest
from app.core.llm.matcher.resume_to_jobs import ResumeToJobsMatcher
from app.core.llm.matcher.job_to_resumes import JobToResumesMatcher

class TestResumeToJobsMatcher:
    def test_match_calculation(self):
        matcher = ResumeToJobsMatcher()
        scores = {
            'skills_match': 80,
            'experience_alignment': 70,
            'education_fit': 90
        }
        result = matcher._calculate_overall_score(scores)
        assert isinstance(result, int)
        assert 0 <= result <= 100

class TestJobToResumesMatcher:
    def test_match_calculation(self):
        matcher = JobToResumesMatcher()
        scores = {
            'skills_match': 80,
            'experience_alignment': 70,
            'education_fit': 90
        }
        result = matcher._calculate_overall_score(scores)
        assert isinstance(result, int)
        assert 0 <= result <= 100
import pytest
from app.core.llm.parser.resume_parser import <PERSON><PERSON><PERSON>ars<PERSON>
from app.core.llm.parser.job_parser import JobParser

class TestResumeParser:
    def test_parse_valid_resume(self):
        # Test parsing valid resume
        pass

    def test_parse_invalid_resume(self):
        # Test parsing invalid resume
        pass

class TestJobParser:
    def test_parse_valid_job(self):
        # Test parsing valid job posting
        pass

    def test_parse_invalid_job(self):
        # Test parsing invalid job posting
        pass